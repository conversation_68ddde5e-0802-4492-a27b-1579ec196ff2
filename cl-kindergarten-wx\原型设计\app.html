<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创联幼儿园核算系统 - 整合版</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/index.css">
    <link rel="stylesheet" href="css/employee.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/app.css">
</head>
<body>
    <div class="app-container">
        <!-- 登录页面 -->
        <div class="page" id="loginPage">
            <div class="container">
                <!-- 头部logo区域 -->
                <div class="header">
                    <div class="logo">
                        <div class="logo-icon">🏫</div>
                        <h1 class="title">创联幼儿园</h1>
                        <p class="subtitle">智能核算系统</p>
                    </div>
                </div>

                <!-- 角色选择区域 -->
                <div class="role-selection">
                    <h2 class="section-title">请选择您的身份</h2>
                    <div class="role-cards">
                        <div class="role-card" data-role="employee">
                            <div class="role-icon">👩‍🏫</div>
                            <h3>员工</h3>
                            <p>考勤管理<br>学生管理<br>工资查询</p>
                        </div>
                        <div class="role-card" data-role="admin">
                            <div class="role-icon">👨‍💼</div>
                            <h3>管理员</h3>
                            <p>财务统计<br>系统管理<br>数据报表</p>
                        </div>
                    </div>
                </div>

                <!-- 登录区域 -->
                <div class="login-section" id="loginSection" style="display: none;">
                    <div class="login-form">
                        <h3 id="loginTitle">员工登录</h3>
                        <div class="form-group">
                            <input type="text" id="username" placeholder="请输入工号或手机号" class="form-input">
                        </div>
                        <div class="form-group">
                            <input type="password" id="password" placeholder="请输入密码" class="form-input">
                        </div>
                        <button class="login-btn" id="loginBtn">登录</button>
                        <div class="login-options">
                            <a href="#" class="link">忘记密码？</a>
                            <a href="#" class="link">微信快捷登录</a>
                        </div>
                    </div>
                </div>

                <!-- 底部信息 -->
                <div class="footer">
                    <p>&copy; 2024 创联幼儿园核算系统</p>
                    <p>让幼儿园财务管理更简单、更高效</p>
                </div>
            </div>
        </div>

        <!-- 员工端工作台 -->
        <div class="page" id="employeePage" style="display: none;">
            <div class="container">
                <!-- 顶部信息栏 -->
                <div class="header">
                    <div class="user-info">
                        <div class="avatar">👩‍🏫</div>
                        <div class="info">
                            <h2 id="empUserName">张老师</h2>
                            <p id="empUserPosition">小班班主任</p>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button class="header-btn" onclick="logout()">退出</button>
                    </div>
                </div>

                <!-- 考勤管理 -->
                <div class="attendance-section">
                    <h3 class="section-title">考勤管理</h3>

                    <!-- 今日概览 -->
                    <div class="today-overview">
                        <div class="overview-card">
                            <div class="card-icon">👶</div>
                            <div class="card-info">
                                <h4>班级人数</h4>
                                <p class="number">25人</p>
                            </div>
                        </div>
                        <div class="overview-card">
                            <div class="card-icon">✅</div>
                            <div class="card-info">
                                <h4>已到园</h4>
                                <p class="number">23人</p>
                            </div>
                        </div>
                        <div class="overview-card">
                            <div class="card-icon">❌</div>
                            <div class="card-info">
                                <h4>未到园</h4>
                                <p class="number">2人</p>
                            </div>
                        </div>
                    </div>

                    <!-- 快捷操作 -->
                    <div class="quick-actions">
                        <button class="action-btn primary" onclick="showStudentAttendanceManagement()">
                            <span class="btn-icon">📝</span>
                            学生考勤
                        </button>
                        <button class="action-btn secondary" onclick="scanQRCode()">
                            <span class="btn-icon">📱</span>
                            扫码签到
                        </button>
                        <button class="action-btn secondary" onclick="exportAttendance()">
                            <span class="btn-icon">📊</span>
                            导出考勤
                        </button>
                    </div>

                    <!-- 今日考勤列表 -->
                    <div class="today-attendance">
                        <h4>今日考勤</h4>
                        <div class="attendance-list">
                            <div class="attendance-item">
                                <div class="student-avatar">👶</div>
                                <div class="student-info">
                                    <h5>张小明</h5>
                                    <p>入园: 08:15 | 离园: --:--</p>
                                </div>
                                <div class="status present">✅</div>
                            </div>
                            <div class="attendance-item">
                                <div class="student-avatar">👶</div>
                                <div class="student-info">
                                    <h5>李小红</h5>
                                    <p>入园: 08:20 | 离园: --:--</p>
                                </div>
                                <div class="status present">✅</div>
                            </div>
                            <div class="attendance-item">
                                <div class="student-avatar">👶</div>
                                <div class="student-info">
                                    <h5>王小华</h5>
                                    <p>未到园</p>
                                </div>
                                <div class="status absent">❌</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- 管理员端页面 -->
        <div class="page" id="adminPage" style="display: none;">
            <div class="container">
                <!-- 头部信息栏 -->
                <div class="header">
                    <div class="admin-info">
                        <div class="avatar">👨‍💼</div>
                        <div class="info">
                            <h2 id="adminName">园长</h2>
                            <p id="adminPosition">系统管理员</p>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button class="header-btn" onclick="logout()">退出</button>
                    </div>
                </div>

                <!-- 管理功能 -->
                <div class="management-section">
                    <h3 class="section-title">管理功能</h3>
                    <div class="management-grid">
                        <div class="management-item" onclick="showStudentAttendanceManagement()">
                            <div class="management-icon">📝</div>
                            <span>学生考勤管理</span>
                        </div>
                        <div class="management-item" onclick="showTeacherAttendanceManagement()">
                            <div class="management-icon">👩‍🏫</div>
                            <span>教师考勤管理</span>
                        </div>
                        <div class="management-item" onclick="showClassManagement()">
                            <div class="management-icon">🏫</div>
                            <span>班级管理</span>
                        </div>
                        <div class="management-item" onclick="showUserManagement()">
                            <div class="management-icon">👥</div>
                            <span>用户管理</span>
                        </div>
                        <div class="management-item" onclick="showCourseManagement()">
                            <div class="management-icon">📚</div>
                            <span>课程管理</span>
                        </div>
                        <div class="management-item" onclick="showTuitionManagement()">
                            <div class="management-icon">💰</div>
                            <span>园费管理</span>
                        </div>
                        <div class="management-item" onclick="showStudentManagement()">
                            <div class="management-icon">👶</div>
                            <span>学生管理</span>
                        </div>
                        <div class="management-item" onclick="showTeacherManagement()">
                            <div class="management-icon">👩‍🏫</div>
                            <span>教师管理</span>
                        </div>
                        <div class="management-item" onclick="showInventoryManagement()">
                            <div class="management-icon">📦</div>
                            <span>库存管理</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学生考勤管理页面 -->
        <div class="page" id="studentAttendanceManagementPage" style="display: none;">
            <div class="container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <button class="back-btn" onclick="goBack()">←</button>
                    <h1 class="navbar-title">学生考勤管理</h1>
                </div>

                <!-- 日期选择 -->
                <div class="date-selector">
                    <div class="date-nav">
                        <button class="date-btn" onclick="changeAttendanceDate(-1)">←</button>
                        <h3 id="attendanceDate">2024年11月30日 周六</h3>
                        <button class="date-btn" onclick="changeAttendanceDate(1)">→</button>
                    </div>
                    <div class="date-stats">
                        <span class="stat-item">总人数: <strong>75</strong></span>
                        <span class="stat-item">已到: <strong>68</strong></span>
                        <span class="stat-item">未到: <strong>7</strong></span>
                    </div>
                </div>

                <!-- 班级筛选 -->
                <div class="class-filter">
                    <button class="filter-btn active" onclick="filterByClass('all')">全部</button>
                    <button class="filter-btn" onclick="filterByClass('small')">小班</button>
                    <button class="filter-btn" onclick="filterByClass('medium')">中班</button>
                    <button class="filter-btn" onclick="filterByClass('large')">大班</button>
                </div>

                <!-- 考勤列表 -->
                <div class="attendance-management-list">
                    <!-- 小班 -->
                    <div class="class-section" data-class="small">
                        <h4 class="class-title">小班 (25人)</h4>
                        <div class="student-attendance-card">
                            <div class="student-avatar">👶</div>
                            <div class="student-info">
                                <h5>张小明</h5>
                                <p>学号: 2024001</p>
                            </div>
                            <div class="attendance-time">
                                <span class="check-in">入园: 08:15</span>
                                <span class="check-out">离园: --:--</span>
                            </div>
                            <div class="attendance-actions">
                                <div class="status present">已到园</div>
                                <button class="action-btn" onclick="editAttendance(1)">编辑</button>
                            </div>
                        </div>

                        <div class="student-attendance-card">
                            <div class="student-avatar">👶</div>
                            <div class="student-info">
                                <h5>李小红</h5>
                                <p>学号: 2024002</p>
                            </div>
                            <div class="attendance-time">
                                <span class="check-in">入园: 08:20</span>
                                <span class="check-out">离园: --:--</span>
                            </div>
                            <div class="attendance-actions">
                                <div class="status present">已到园</div>
                                <button class="action-btn" onclick="editAttendance(2)">编辑</button>
                            </div>
                        </div>

                        <div class="student-attendance-card">
                            <div class="student-avatar">👶</div>
                            <div class="student-info">
                                <h5>王小华</h5>
                                <p>学号: 2024003</p>
                            </div>
                            <div class="attendance-time">
                                <span class="absent-reason">未到园</span>
                            </div>
                            <div class="attendance-actions">
                                <div class="status absent">未到园</div>
                                <button class="action-btn" onclick="markPresent(3)">签到</button>
                            </div>
                        </div>
                    </div>

                    <!-- 中班 -->
                    <div class="class-section" data-class="medium">
                        <h4 class="class-title">中班 (28人)</h4>
                        <div class="student-attendance-card">
                            <div class="student-avatar">👶</div>
                            <div class="student-info">
                                <h5>赵小美</h5>
                                <p>学号: 2024025</p>
                            </div>
                            <div class="attendance-time">
                                <span class="leave-reason">病假 - 发烧</span>
                            </div>
                            <div class="attendance-actions">
                                <div class="status leave">病假</div>
                                <button class="action-btn" onclick="editAttendance(25)">编辑</button>
                            </div>
                        </div>
                    </div>

                    <!-- 大班 -->
                    <div class="class-section" data-class="large">
                        <h4 class="class-title">大班 (22人)</h4>
                        <div class="student-attendance-card">
                            <div class="student-avatar">👶</div>
                            <div class="student-info">
                                <h5>孙小强</h5>
                                <p>学号: 2024050</p>
                            </div>
                            <div class="attendance-time">
                                <span class="check-in">入园: 07:45</span>
                                <span class="check-out">离园: --:--</span>
                            </div>
                            <div class="attendance-actions">
                                <div class="status present">已到园</div>
                                <button class="action-btn" onclick="editAttendance(50)">编辑</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 批量操作 -->
                <div class="batch-operations">
                    <button class="batch-btn" onclick="batchCheckIn()">批量签到</button>
                    <button class="batch-btn" onclick="batchCheckOut()">批量签退</button>
                </div>
            </div>
        </div>

        <!-- 教师考勤管理页面 -->
        <div class="page" id="teacherAttendanceManagementPage" style="display: none;">
            <div class="container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <button class="back-btn" onclick="goBack()">←</button>
                    <h1 class="navbar-title">教师考勤管理</h1>
                </div>

                <!-- 日期选择 -->
                <div class="date-selector">
                    <div class="date-nav">
                        <button class="date-btn" onclick="changeTeacherAttendanceDate(-1)">←</button>
                        <h3 id="teacherAttendanceDate">2024年11月30日 周六</h3>
                        <button class="date-btn" onclick="changeTeacherAttendanceDate(1)">→</button>
                    </div>
                    <div class="date-stats">
                        <span class="stat-item">总人数: <strong>8</strong></span>
                        <span class="stat-item">已签到: <strong>7</strong></span>
                        <span class="stat-item">未签到: <strong>1</strong></span>
                    </div>
                </div>

                <!-- 教师考勤列表 -->
                <div class="teacher-attendance-list">
                    <div class="teacher-attendance-card">
                        <div class="teacher-avatar">👩‍🏫</div>
                        <div class="teacher-info">
                            <h5>张老师</h5>
                            <p>工号: T001 | 小班班主任</p>
                        </div>
                        <div class="attendance-time">
                            <span class="check-in">签到: 08:00</span>
                            <span class="check-out">签退: --:--</span>
                        </div>
                        <div class="attendance-actions">
                            <div class="status present">已签到</div>
                            <button class="action-btn" onclick="editTeacherAttendance(1)">编辑</button>
                        </div>
                    </div>

                    <div class="teacher-attendance-card">
                        <div class="teacher-avatar">👨‍🏫</div>
                        <div class="teacher-info">
                            <h5>李老师</h5>
                            <p>工号: T002 | 中班班主任</p>
                        </div>
                        <div class="attendance-time">
                            <span class="check-in">签到: 07:55</span>
                            <span class="check-out">签退: --:--</span>
                        </div>
                        <div class="attendance-actions">
                            <div class="status present">已签到</div>
                            <button class="action-btn" onclick="editTeacherAttendance(2)">编辑</button>
                        </div>
                    </div>

                    <div class="teacher-attendance-card">
                        <div class="teacher-avatar">👩‍🏫</div>
                        <div class="teacher-info">
                            <h5>王老师</h5>
                            <p>工号: T003 | 托管教师</p>
                        </div>
                        <div class="attendance-time">
                            <span class="absent-reason">未签到</span>
                        </div>
                        <div class="attendance-actions">
                            <div class="status absent">未签到</div>
                            <button class="action-btn" onclick="markTeacherPresent(3)">签到</button>
                        </div>
                    </div>
                </div>

                <!-- 批量操作 -->
                <div class="batch-operations">
                    <button class="batch-btn" onclick="batchTeacherCheckIn()">批量签到</button>
                    <button class="batch-btn" onclick="batchTeacherCheckOut()">批量签退</button>
                </div>
            </div>
        </div>

        <!-- 学生管理页面 -->
        <div class="page" id="studentManagementPage" style="display: none;">
            <div class="container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <button class="back-btn" onclick="goBack()">←</button>
                    <h1 class="navbar-title">学生管理</h1>
                    <button class="btn" onclick="addStudent()">添加</button>
                </div>

                <!-- 搜索栏 -->
                <div class="search-section">
                    <input type="text" class="search-input" placeholder="搜索学生姓名或学号" id="studentSearch">
                    <button class="search-btn" onclick="searchStudents()">🔍</button>
                </div>

                <!-- 学生列表 -->
                <div class="student-list-section">
                    <div class="student-item-card">
                        <div class="student-avatar">👶</div>
                        <div class="student-details">
                            <h4>张小明</h4>
                            <p>学号: 2024001 | 小班</p>
                            <p>家长: 张先生 | 电话: 138****1234</p>
                            <p class="balance">预存款余额: ¥1,200.00</p>
                        </div>
                        <div class="student-actions">
                            <button class="action-btn recharge" onclick="rechargeBalance(1)">充值</button>
                            <button class="action-btn edit" onclick="editStudent(1)">编辑</button>
                        </div>
                    </div>

                    <div class="student-item-card">
                        <div class="student-avatar">👶</div>
                        <div class="student-details">
                            <h4>李小红</h4>
                            <p>学号: 2024002 | 小班</p>
                            <p>家长: 李女士 | 电话: 139****5678</p>
                            <p class="balance">预存款余额: ¥800.50</p>
                        </div>
                        <div class="student-actions">
                            <button class="action-btn recharge" onclick="rechargeBalance(2)">充值</button>
                            <button class="action-btn edit" onclick="editStudent(2)">编辑</button>
                        </div>
                    </div>

                    <div class="student-item-card">
                        <div class="student-avatar">👶</div>
                        <div class="student-details">
                            <h4>王小华</h4>
                            <p>学号: 2024003 | 中班</p>
                            <p>家长: 王先生 | 电话: 137****9012</p>
                            <p class="balance low-balance">预存款余额: ¥150.00</p>
                        </div>
                        <div class="student-actions">
                            <button class="action-btn recharge" onclick="rechargeBalance(3)">充值</button>
                            <button class="action-btn edit" onclick="editStudent(3)">编辑</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加学生页面 -->
        <div class="page" id="addStudentPage" style="display: none;">
            <div class="container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <button class="back-btn" onclick="goBack()">←</button>
                    <h1 class="navbar-title">添加学生</h1>
                    <button class="btn" onclick="saveStudent()">保存</button>
                </div>

                <!-- 学生信息表单 -->
                <div class="form-section">
                    <div class="form-group">
                        <label>学生姓名 *</label>
                        <input type="text" id="studentName" placeholder="请输入学生姓名" required>
                    </div>

                    <div class="form-group">
                        <label>性别 *</label>
                        <select id="studentGender" required>
                            <option value="">请选择性别</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>出生日期 *</label>
                        <input type="date" id="studentBirthDate" required>
                    </div>

                    <div class="form-group">
                        <label>班级 *</label>
                        <select id="studentClass" required>
                            <option value="">请选择班级</option>
                            <option value="小班">小班</option>
                            <option value="中班">中班</option>
                            <option value="大班">大班</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>入园日期 *</label>
                        <input type="date" id="enrollmentDate" required>
                    </div>

                    <div class="form-group">
                        <label>家长姓名 *</label>
                        <input type="text" id="parentName" placeholder="请输入家长姓名" required>
                    </div>

                    <div class="form-group">
                        <label>家长电话 *</label>
                        <input type="tel" id="parentPhone" placeholder="请输入家长电话" required>
                    </div>

                    <div class="form-group">
                        <label>家庭地址</label>
                        <textarea id="homeAddress" placeholder="请输入家庭地址" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label>预存款余额</label>
                        <input type="number" id="prepaidBalance" placeholder="0.00" step="0.01" min="0">
                    </div>

                    <div class="form-group">
                        <label>健康状况</label>
                        <textarea id="healthStatus" placeholder="请输入健康状况说明" rows="2"></textarea>
                    </div>

                    <div class="form-group">
                        <label>过敏信息</label>
                        <textarea id="allergyInfo" placeholder="请输入过敏信息" rows="2"></textarea>
                    </div>

                    <div class="form-group">
                        <label>备注</label>
                        <textarea id="studentRemarks" placeholder="请输入备注信息" rows="3"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- 园费管理页面 -->
        <div class="page" id="tuitionManagementPage" style="display: none;">
            <div class="container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <button class="back-btn" onclick="goBack()">←</button>
                    <h1 class="navbar-title">园费管理</h1>
                    <button class="btn" onclick="generateBills()">生成账单</button>
                </div>

                <!-- 月份选择 -->
                <div class="month-selector">
                    <button class="month-btn" onclick="changeMonth(-1)">←</button>
                    <h3 id="currentMonth">2024年11月</h3>
                    <button class="month-btn" onclick="changeMonth(1)">→</button>
                </div>

                <!-- 重新核算按钮 -->
                <div class="recalculate-section">
                    <button class="btn-recalculate" onclick="recalculateFinance()">
                        🔄 重新核算本月财务
                    </button>
                </div>

                <!-- 学生账单列表 -->
                <div class="bill-list-section">
                    <h3 class="section-title">学生账单</h3>
                    <div class="bill-item">
                        <div class="bill-info">
                            <h4>张小明 (小班)</h4>
                            <p>出勤: 18天 | 餐费: ¥360 | 保教费: ¥1,200</p>
                            <p class="bill-total">应缴: ¥1,560 | 预存款扣除: ¥1,200 | 待缴: ¥360</p>
                        </div>
                        <div class="bill-status paid">已缴费</div>
                    </div>

                    <div class="bill-item">
                        <div class="bill-info">
                            <h4>李小红 (小班)</h4>
                            <p>出勤: 20天 | 餐费: ¥400 | 保教费: ¥1,200</p>
                            <p class="bill-total">应缴: ¥1,600 | 预存款扣除: ¥800 | 待缴: ¥800</p>
                        </div>
                        <div class="bill-status unpaid">待缴费</div>
                    </div>

                    <div class="bill-item">
                        <div class="bill-info">
                            <h4>王小华 (中班)</h4>
                            <p>出勤: 15天 | 餐费: ¥300 | 保教费: ¥600</p>
                            <p class="bill-total">应缴: ¥900 | 预存款扣除: ¥150 | 待缴: ¥750</p>
                        </div>
                        <div class="bill-status unpaid">待缴费</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 班级管理页面 -->
        <div class="page" id="classManagementPage" style="display: none;">
            <div class="container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <button class="back-btn" onclick="goBack()">←</button>
                    <h1 class="navbar-title">班级管理</h1>
                    <button class="btn" onclick="addClass()">添加班级</button>
                </div>

                <!-- 班级列表 -->
                <div class="class-list-section">
                    <div class="class-item-card">
                        <div class="class-icon">🎈</div>
                        <div class="class-details">
                            <h4>小班</h4>
                            <p>班级代码: XB001 | 年龄段: 3-4岁</p>
                            <p>班主任: 张老师 | 副班主任: 刘老师</p>
                            <p>学生人数: 25人 | 最大容量: 30人</p>
                            <p>教室: 一楼101室</p>
                        </div>
                        <div class="class-actions">
                            <button class="action-btn edit" onclick="editClass(1)">编辑</button>
                            <button class="action-btn view" onclick="viewClassStudents(1)">学生</button>
                        </div>
                    </div>

                    <div class="class-item-card">
                        <div class="class-icon">🎨</div>
                        <div class="class-details">
                            <h4>中班</h4>
                            <p>班级代码: ZB001 | 年龄段: 4-5岁</p>
                            <p>班主任: 李老师 | 副班主任: 王老师</p>
                            <p>学生人数: 28人 | 最大容量: 30人</p>
                            <p>教室: 二楼201室</p>
                        </div>
                        <div class="class-actions">
                            <button class="action-btn edit" onclick="editClass(2)">编辑</button>
                            <button class="action-btn view" onclick="viewClassStudents(2)">学生</button>
                        </div>
                    </div>

                    <div class="class-item-card">
                        <div class="class-icon">🎓</div>
                        <div class="class-details">
                            <h4>大班</h4>
                            <p>班级代码: DB001 | 年龄段: 5-6岁</p>
                            <p>班主任: 王老师 | 副班主任: 陈老师</p>
                            <p>学生人数: 22人 | 最大容量: 25人</p>
                            <p>教室: 三楼301室</p>
                        </div>
                        <div class="class-actions">
                            <button class="action-btn edit" onclick="editClass(3)">编辑</button>
                            <button class="action-btn view" onclick="viewClassStudents(3)">学生</button>
                        </div>
                    </div>

                    <div class="class-item-card">
                        <div class="class-icon">🌟</div>
                        <div class="class-details">
                            <h4>托管班</h4>
                            <p>班级代码: TG001 | 年龄段: 3-6岁</p>
                            <p>班主任: 赵老师 | 副班主任: 孙老师</p>
                            <p>学生人数: 15人 | 最大容量: 20人</p>
                            <p>教室: 一楼102室</p>
                        </div>
                        <div class="class-actions">
                            <button class="action-btn edit" onclick="editClass(4)">编辑</button>
                            <button class="action-btn view" onclick="viewClassStudents(4)">学生</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加班级页面 -->
        <div class="page" id="addClassPage" style="display: none;">
            <div class="container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <button class="back-btn" onclick="goBack()">←</button>
                    <h1 class="navbar-title">添加班级</h1>
                    <button class="btn" onclick="saveClass()">保存</button>
                </div>

                <!-- 班级信息表单 -->
                <div class="form-section">
                    <div class="form-group">
                        <label>班级名称 *</label>
                        <input type="text" id="className" placeholder="请输入班级名称" required>
                    </div>

                    <div class="form-group">
                        <label>班级代码 *</label>
                        <input type="text" id="classCode" placeholder="请输入班级代码" required>
                    </div>

                    <div class="form-group">
                        <label>班级类型 *</label>
                        <select id="classType" required>
                            <option value="">请选择班级类型</option>
                            <option value="小班">小班</option>
                            <option value="中班">中班</option>
                            <option value="大班">大班</option>
                            <option value="托管班">托管班</option>
                            <option value="兴趣班">兴趣班</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>适用年龄段 *</label>
                        <input type="text" id="ageRange" placeholder="例：3-4岁" required>
                    </div>

                    <div class="form-group">
                        <label>班主任 *</label>
                        <select id="headTeacher" required>
                            <option value="">请选择班主任</option>
                            <option value="张老师">张老师</option>
                            <option value="李老师">李老师</option>
                            <option value="王老师">王老师</option>
                            <option value="赵老师">赵老师</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>副班主任</label>
                        <select id="assistantTeacher">
                            <option value="">请选择副班主任</option>
                            <option value="刘老师">刘老师</option>
                            <option value="陈老师">陈老师</option>
                            <option value="孙老师">孙老师</option>
                            <option value="周老师">周老师</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>最大容量 *</label>
                        <input type="number" id="maxCapacity" placeholder="0" min="1" required>
                    </div>

                    <div class="form-group">
                        <label>教室位置</label>
                        <input type="text" id="classroom" placeholder="请输入教室位置">
                    </div>

                    <div class="form-group">
                        <label>学费标准</label>
                        <input type="number" id="tuitionFee" placeholder="0.00" step="0.01" min="0">
                    </div>

                    <div class="form-group">
                        <label>餐费标准</label>
                        <input type="number" id="mealFee" placeholder="0.00" step="0.01" min="0">
                    </div>

                    <div class="form-group">
                        <label>开班日期</label>
                        <input type="date" id="startDate">
                    </div>

                    <div class="form-group">
                        <label>班级状态</label>
                        <select id="classStatus">
                            <option value="正常">正常</option>
                            <option value="暂停">暂停</option>
                            <option value="停班">停班</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>班级描述</label>
                        <textarea id="classDescription" placeholder="请输入班级描述" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label>备注</label>
                        <textarea id="classRemarks" placeholder="请输入备注信息" rows="3"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- 课程管理页面 -->
        <div class="page" id="courseManagementPage" style="display: none;">
            <div class="container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <button class="back-btn" onclick="goBack()">←</button>
                    <h1 class="navbar-title">课程管理</h1>
                    <button class="btn" onclick="addCourse()">添加课程</button>
                </div>

                <!-- 课程列表 -->
                <div class="course-list-section">
                    <div class="course-item-card">
                        <div class="course-icon">🎨</div>
                        <div class="course-details">
                            <h4>美术课</h4>
                            <p>时间: 周一、周三 15:00-16:00</p>
                            <p>费用: ¥40/节 | 已报名: 15人</p>
                            <p>教师: 李老师</p>
                        </div>
                        <div class="course-actions">
                            <button class="action-btn edit" onclick="editCourse(1)">编辑</button>
                            <button class="action-btn view" onclick="viewCourseStudents(1)">学员</button>
                        </div>
                    </div>

                    <div class="course-item-card">
                        <div class="course-icon">🎵</div>
                        <div class="course-details">
                            <h4>音乐课</h4>
                            <p>时间: 周二、周四 14:30-15:30</p>
                            <p>费用: ¥35/节 | 已报名: 12人</p>
                            <p>教师: 王老师</p>
                        </div>
                        <div class="course-actions">
                            <button class="action-btn edit" onclick="editCourse(2)">编辑</button>
                            <button class="action-btn view" onclick="viewCourseStudents(2)">学员</button>
                        </div>
                    </div>

                    <div class="course-item-card">
                        <div class="course-icon">🏃</div>
                        <div class="course-details">
                            <h4>体育课</h4>
                            <p>时间: 周五 16:00-17:00</p>
                            <p>费用: ¥30/节 | 已报名: 20人</p>
                            <p>教师: 张老师</p>
                        </div>
                        <div class="course-actions">
                            <button class="action-btn edit" onclick="editCourse(3)">编辑</button>
                            <button class="action-btn view" onclick="viewCourseStudents(3)">学员</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加课程页面 -->
        <div class="page" id="addCoursePage" style="display: none;">
            <div class="container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <button class="back-btn" onclick="goBack()">←</button>
                    <h1 class="navbar-title">添加课程</h1>
                    <button class="btn" onclick="saveCourse()">保存</button>
                </div>

                <!-- 课程信息表单 -->
                <div class="form-section">
                    <div class="form-group">
                        <label>课程名称 *</label>
                        <input type="text" id="courseName" placeholder="请输入课程名称" required>
                    </div>

                    <div class="form-group">
                        <label>课程类型 *</label>
                        <select id="courseType" required>
                            <option value="">请选择课程类型</option>
                            <option value="必修课">必修课</option>
                            <option value="选修课">选修课</option>
                            <option value="兴趣班">兴趣班</option>
                            <option value="托管课">托管课</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>适用班级 *</label>
                        <select id="targetClass" required>
                            <option value="">请选择适用班级</option>
                            <option value="小班">小班</option>
                            <option value="中班">中班</option>
                            <option value="大班">大班</option>
                            <option value="全部">全部班级</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>授课教师 *</label>
                        <select id="courseTeacher" required>
                            <option value="">请选择授课教师</option>
                            <option value="张老师">张老师</option>
                            <option value="李老师">李老师</option>
                            <option value="王老师">王老师</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>上课时间 *</label>
                        <input type="text" id="courseSchedule" placeholder="例：周一、周三 15:00-16:00" required>
                    </div>

                    <div class="form-group">
                        <label>课程费用</label>
                        <input type="number" id="courseFee" placeholder="0.00" step="0.01" min="0">
                    </div>

                    <div class="form-group">
                        <label>收费方式</label>
                        <select id="feeType">
                            <option value="">请选择收费方式</option>
                            <option value="按节收费">按节收费</option>
                            <option value="按月收费">按月收费</option>
                            <option value="按学期收费">按学期收费</option>
                            <option value="免费">免费</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>最大人数</label>
                        <input type="number" id="maxStudents" placeholder="0" min="0">
                    </div>

                    <div class="form-group">
                        <label>上课地点</label>
                        <input type="text" id="courseLocation" placeholder="请输入上课地点">
                    </div>

                    <div class="form-group">
                        <label>课程描述</label>
                        <textarea id="courseDescription" placeholder="请输入课程描述" rows="4"></textarea>
                    </div>

                    <div class="form-group">
                        <label>教学目标</label>
                        <textarea id="teachingGoals" placeholder="请输入教学目标" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label>所需教具</label>
                        <textarea id="requiredMaterials" placeholder="请输入所需教具" rows="2"></textarea>
                    </div>

                    <div class="form-group">
                        <label>备注</label>
                        <textarea id="courseRemarks" placeholder="请输入备注信息" rows="3"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- 教师管理页面 -->
        <div class="page" id="teacherManagementPage" style="display: none;">
            <div class="container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <button class="back-btn" onclick="goBack()">←</button>
                    <h1 class="navbar-title">教师管理</h1>
                    <button class="btn" onclick="addTeacher()">添加教师</button>
                </div>

                <!-- 教师列表 -->
                <div class="teacher-list-section">
                    <div class="teacher-item-card">
                        <div class="teacher-avatar">👩‍🏫</div>
                        <div class="teacher-details">
                            <h4>张老师</h4>
                            <p>工号: T001 | 小班班主任</p>
                            <p>电话: 138****1111 | 入职: 2020-09-01</p>
                            <p>本月工资: ¥4,500 | 课时费: ¥600</p>
                        </div>
                        <div class="teacher-actions">
                            <button class="action-btn edit" onclick="editTeacher(1)">编辑</button>
                            <button class="action-btn salary" onclick="viewSalary(1)">工资</button>
                        </div>
                    </div>

                    <div class="teacher-item-card">
                        <div class="teacher-avatar">👨‍🏫</div>
                        <div class="teacher-details">
                            <h4>李老师</h4>
                            <p>工号: T002 | 中班班主任</p>
                            <p>电话: 139****2222 | 入职: 2019-03-15</p>
                            <p>本月工资: ¥4,800 | 课时费: ¥800</p>
                        </div>
                        <div class="teacher-actions">
                            <button class="action-btn edit" onclick="editTeacher(2)">编辑</button>
                            <button class="action-btn salary" onclick="viewSalary(2)">工资</button>
                        </div>
                    </div>

                    <div class="teacher-item-card">
                        <div class="teacher-avatar">👩‍🏫</div>
                        <div class="teacher-details">
                            <h4>王老师</h4>
                            <p>工号: T003 | 托管教师</p>
                            <p>电话: 137****3333 | 入职: 2021-06-01</p>
                            <p>本月工资: ¥3,800 | 课时费: ¥1,200</p>
                        </div>
                        <div class="teacher-actions">
                            <button class="action-btn edit" onclick="editTeacher(3)">编辑</button>
                            <button class="action-btn salary" onclick="viewSalary(3)">工资</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加教师页面 -->
        <div class="page" id="addTeacherPage" style="display: none;">
            <div class="container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <button class="back-btn" onclick="goBack()">←</button>
                    <h1 class="navbar-title">添加教师</h1>
                    <button class="btn" onclick="saveTeacher()">保存</button>
                </div>

                <!-- 教师信息表单 -->
                <div class="form-section">
                    <div class="form-group">
                        <label>教师姓名 *</label>
                        <input type="text" id="teacherName" placeholder="请输入教师姓名" required>
                    </div>

                    <div class="form-group">
                        <label>性别 *</label>
                        <select id="teacherGender" required>
                            <option value="">请选择性别</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>出生日期 *</label>
                        <input type="date" id="teacherBirthDate" required>
                    </div>

                    <div class="form-group">
                        <label>联系电话 *</label>
                        <input type="tel" id="teacherPhone" placeholder="请输入联系电话" required>
                    </div>

                    <div class="form-group">
                        <label>身份证号 *</label>
                        <input type="text" id="teacherIdCard" placeholder="请输入身份证号" required>
                    </div>

                    <div class="form-group">
                        <label>职位 *</label>
                        <select id="teacherPosition" required>
                            <option value="">请选择职位</option>
                            <option value="园长">园长</option>
                            <option value="副园长">副园长</option>
                            <option value="班主任">班主任</option>
                            <option value="副班主任">副班主任</option>
                            <option value="托管教师">托管教师</option>
                            <option value="保育员">保育员</option>
                            <option value="厨师">厨师</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>入职日期 *</label>
                        <input type="date" id="hireDate" required>
                    </div>

                    <div class="form-group">
                        <label>基本工资</label>
                        <input type="number" id="baseSalary" placeholder="0.00" step="0.01" min="0">
                    </div>

                    <div class="form-group">
                        <label>课时费</label>
                        <input type="number" id="hourlyRate" placeholder="0.00" step="0.01" min="0">
                    </div>

                    <div class="form-group">
                        <label>学历</label>
                        <select id="education">
                            <option value="">请选择学历</option>
                            <option value="高中">高中</option>
                            <option value="大专">大专</option>
                            <option value="本科">本科</option>
                            <option value="硕士">硕士</option>
                            <option value="博士">博士</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>专业</label>
                        <input type="text" id="major" placeholder="请输入专业">
                    </div>

                    <div class="form-group">
                        <label>毕业院校</label>
                        <input type="text" id="graduateSchool" placeholder="请输入毕业院校">
                    </div>

                    <div class="form-group">
                        <label>家庭地址</label>
                        <textarea id="teacherAddress" placeholder="请输入家庭地址" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label>备注</label>
                        <textarea id="teacherRemarks" placeholder="请输入备注信息" rows="3"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- 库存管理页面 -->
        <div class="page" id="inventoryManagementPage" style="display: none;">
            <div class="container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <button class="back-btn" onclick="goBack()">←</button>
                    <h1 class="navbar-title">库存管理</h1>
                    <button class="btn" onclick="addInventory()">添加物品</button>
                </div>

                <!-- 库存分类 -->
                <div class="inventory-tabs">
                    <button class="tab-btn active" onclick="switchInventoryTab('all')">全部</button>
                    <button class="tab-btn" onclick="switchInventoryTab('food')">食材</button>
                    <button class="tab-btn" onclick="switchInventoryTab('supplies')">用品</button>
                    <button class="tab-btn" onclick="switchInventoryTab('toys')">玩具</button>
                </div>

                <!-- 库存列表 -->
                <div class="inventory-list-section">
                    <div class="inventory-item-card" data-category="food">
                        <div class="inventory-icon">🥬</div>
                        <div class="inventory-details">
                            <h4>青菜</h4>
                            <p>规格: 500g/份 | 单价: ¥3.50</p>
                            <p class="stock-info">库存: 25份 | 预警线: 10份</p>
                            <p>供应商: 新鲜蔬菜配送</p>
                        </div>
                        <div class="inventory-actions">
                            <button class="action-btn in" onclick="stockIn(1)">入库</button>
                            <button class="action-btn out" onclick="stockOut(1)">出库</button>
                        </div>
                    </div>

                    <div class="inventory-item-card" data-category="supplies">
                        <div class="inventory-icon">🧻</div>
                        <div class="inventory-details">
                            <h4>纸巾</h4>
                            <p>规格: 200抽/包 | 单价: ¥8.00</p>
                            <p class="stock-info low-stock">库存: 5包 | 预警线: 10包</p>
                            <p>供应商: 日用品批发</p>
                        </div>
                        <div class="inventory-actions">
                            <button class="action-btn in" onclick="stockIn(2)">入库</button>
                            <button class="action-btn out" onclick="stockOut(2)">出库</button>
                        </div>
                    </div>

                    <div class="inventory-item-card" data-category="toys">
                        <div class="inventory-icon">🧸</div>
                        <div class="inventory-details">
                            <h4>积木玩具</h4>
                            <p>规格: 100块/套 | 单价: ¥45.00</p>
                            <p class="stock-info">库存: 8套 | 预警线: 5套</p>
                            <p>供应商: 教育玩具专营</p>
                        </div>
                        <div class="inventory-actions">
                            <button class="action-btn in" onclick="stockIn(3)">入库</button>
                            <button class="action-btn out" onclick="stockOut(3)">出库</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加库存物品页面 -->
        <div class="page" id="addInventoryPage" style="display: none;">
            <div class="container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <button class="back-btn" onclick="goBack()">←</button>
                    <h1 class="navbar-title">添加物品</h1>
                    <button class="btn" onclick="saveInventoryItem()">保存</button>
                </div>

                <!-- 物品信息表单 -->
                <div class="form-section">
                    <div class="form-group">
                        <label>物品名称 *</label>
                        <input type="text" id="itemName" placeholder="请输入物品名称" required>
                    </div>

                    <div class="form-group">
                        <label>物品分类 *</label>
                        <select id="itemCategory" required>
                            <option value="">请选择分类</option>
                            <option value="食材">食材</option>
                            <option value="用品">用品</option>
                            <option value="玩具">玩具</option>
                            <option value="教具">教具</option>
                            <option value="清洁用品">清洁用品</option>
                            <option value="办公用品">办公用品</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>规格型号</label>
                        <input type="text" id="itemSpecification" placeholder="请输入规格型号">
                    </div>

                    <div class="form-group">
                        <label>单位 *</label>
                        <select id="itemUnit" required>
                            <option value="">请选择单位</option>
                            <option value="个">个</option>
                            <option value="包">包</option>
                            <option value="盒">盒</option>
                            <option value="瓶">瓶</option>
                            <option value="袋">袋</option>
                            <option value="箱">箱</option>
                            <option value="套">套</option>
                            <option value="份">份</option>
                            <option value="公斤">公斤</option>
                            <option value="升">升</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>单价</label>
                        <input type="number" id="itemPrice" placeholder="0.00" step="0.01" min="0">
                    </div>

                    <div class="form-group">
                        <label>当前库存 *</label>
                        <input type="number" id="currentStock" placeholder="0" min="0" required>
                    </div>

                    <div class="form-group">
                        <label>最低库存预警线 *</label>
                        <input type="number" id="minStock" placeholder="0" min="0" required>
                    </div>

                    <div class="form-group">
                        <label>最高库存上限</label>
                        <input type="number" id="maxStock" placeholder="0" min="0">
                    </div>

                    <div class="form-group">
                        <label>供应商</label>
                        <input type="text" id="supplier" placeholder="请输入供应商名称">
                    </div>

                    <div class="form-group">
                        <label>供应商联系方式</label>
                        <input type="text" id="supplierContact" placeholder="请输入供应商联系方式">
                    </div>

                    <div class="form-group">
                        <label>存放位置</label>
                        <input type="text" id="storageLocation" placeholder="请输入存放位置">
                    </div>

                    <div class="form-group">
                        <label>备注</label>
                        <textarea id="itemRemarks" placeholder="请输入备注信息" rows="3"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户管理页面 -->
        <div class="page" id="userManagementPage" style="display: none;">
            <div class="container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <button class="back-btn" onclick="goBack()">←</button>
                    <h1 class="navbar-title">用户管理</h1>
                    <button class="btn" onclick="addUser()">添加用户</button>
                </div>

                <!-- 用户角色筛选 -->
                <div class="user-tabs">
                    <button class="tab-btn active" onclick="switchUserTab('all')">全部</button>
                    <button class="tab-btn" onclick="switchUserTab('admin')">管理员</button>
                    <button class="tab-btn" onclick="switchUserTab('teacher')">教师</button>
                    <button class="tab-btn" onclick="switchUserTab('parent')">家长</button>
                </div>

                <!-- 用户列表 -->
                <div class="user-list-section">
                    <div class="user-item-card" data-role="admin">
                        <div class="user-avatar">👨‍💼</div>
                        <div class="user-details">
                            <h4>园长</h4>
                            <p>账号: admin | 角色: 系统管理员</p>
                            <p>电话: 138****0000 | 状态: 正常</p>
                            <p>最后登录: 2024-11-30 09:15</p>
                        </div>
                        <div class="user-actions">
                            <button class="action-btn edit" onclick="editUser(1)">编辑</button>
                            <button class="action-btn reset" onclick="resetPassword(1)">重置密码</button>
                        </div>
                    </div>

                    <div class="user-item-card" data-role="teacher">
                        <div class="user-avatar">👩‍🏫</div>
                        <div class="user-details">
                            <h4>张老师</h4>
                            <p>账号: emp001 | 角色: 教师</p>
                            <p>电话: 138****1111 | 状态: 正常</p>
                            <p>最后登录: 2024-11-30 08:30</p>
                        </div>
                        <div class="user-actions">
                            <button class="action-btn edit" onclick="editUser(2)">编辑</button>
                            <button class="action-btn reset" onclick="resetPassword(2)">重置密码</button>
                        </div>
                    </div>

                    <div class="user-item-card" data-role="parent">
                        <div class="user-avatar">👨‍👩‍👧</div>
                        <div class="user-details">
                            <h4>张先生 (张小明家长)</h4>
                            <p>账号: 138****1234 | 角色: 家长</p>
                            <p>电话: 138****1234 | 状态: 正常</p>
                            <p>最后登录: 2024-11-29 18:45</p>
                        </div>
                        <div class="user-actions">
                            <button class="action-btn edit" onclick="editUser(3)">编辑</button>
                            <button class="action-btn reset" onclick="resetPassword(3)">重置密码</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
