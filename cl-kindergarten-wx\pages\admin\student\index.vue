<template>
	<view class="container">
		<!-- 导航栏 -->
		<view class="navbar">
			<button class="back-btn" @click="goBack">←</button>
			<text class="navbar-title">学生管理</text>
			<button class="add-btn" @click="addStudent">添加学生</button>
		</view>

		<!-- 搜索栏 -->
		<view class="search-section">
			<input 
				class="search-input" 
				placeholder="搜索学生姓名或学号" 
				v-model="searchKeyword"
				@input="handleSearch"
			/>
		</view>

		<!-- 学生列表 -->
		<view class="student-list">
			<view v-for="student in filteredStudentList" :key="student.id" class="student-item-card">
				<view class="student-avatar">👶</view>
				<view class="student-details">
					<text class="student-name">{{ student.name }}</text>
					<text class="student-info">学号: {{ student.studentNo }} | {{ student.class }}</text>
					<text class="student-info">家长: {{ student.parentName }} | {{ student.parentPhone }}</text>
					<view class="balance-section">
						<text class="balance-label">预存款余额:</text>
						<text class="balance-amount" :class="{ 'low-balance': student.balance < 100 }">
							¥{{ student.balance.toFixed(2) }}
						</text>
					</view>
				</view>
				<view class="student-actions">
					<button class="action-btn recharge" @click="rechargeBalance(student)">充值</button>
					<button class="action-btn edit" @click="editStudent(student)">编辑</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'

export default {
	data() {
		return {
			searchKeyword: '',
			studentList: [
				{
					id: 1,
					name: '张小明',
					studentNo: '2024001',
					class: '小班',
					parentName: '张爸爸',
					parentPhone: '13800138001',
					balance: 1200.00
				},
				{
					id: 2,
					name: '李小红',
					studentNo: '2024002',
					class: '小班',
					parentName: '李妈妈',
					parentPhone: '13800138002',
					balance: 800.00
				},
				{
					id: 3,
					name: '王小华',
					studentNo: '2024003',
					class: '小班',
					parentName: '王爸爸',
					parentPhone: '13800138003',
					balance: 50.00
				},
				{
					id: 4,
					name: '赵小美',
					studentNo: '2024025',
					class: '中班',
					parentName: '赵妈妈',
					parentPhone: '13800138025',
					balance: 1500.00
				},
				{
					id: 5,
					name: '孙小强',
					studentNo: '2024050',
					class: '大班',
					parentName: '孙爸爸',
					parentPhone: '13800138050',
					balance: 300.00
				}
			]
		}
	},
	computed: {
		filteredStudentList() {
			if (!this.searchKeyword) {
				return this.studentList
			}
			return this.studentList.filter(student => 
				student.name.includes(this.searchKeyword) || 
				student.studentNo.includes(this.searchKeyword)
			)
		}
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},
		
		addStudent() {
			toast('添加学生功能开发中...')
			// useRouter('/pages/admin/student/add', {}, 'navigateTo')
		},
		
		handleSearch() {
			// 搜索逻辑已在computed中处理
		},
		
		rechargeBalance(student) {
			uni.showModal({
				title: '充值预存款',
				content: `为 ${student.name} 充值预存款`,
				confirmText: '充值',
				success: (res) => {
					if (res.confirm) {
						toast(`${student.name} 充值功能开发中...`)
						// 这里可以跳转到充值页面或显示充值弹窗
					}
				}
			})
		},
		
		editStudent(student) {
			toast(`编辑学生: ${student.name}`)
			// useRouter('/pages/admin/student/edit', { id: student.id }, 'navigateTo')
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

.navbar {
	background: white;
	padding: 20rpx 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.back-btn {
	background: none;
	border: none;
	font-size: 36rpx;
	color: #333;
}

.navbar-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	flex: 1;
	text-align: center;
}

.add-btn {
	background: #4CAF50;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 24rpx;
	font-size: 24rpx;
}

.search-section {
	background: white;
	padding: 20rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.search-input {
	width: 100%;
	background: #f5f5f5;
	border: none;
	border-radius: 40rpx;
	padding: 20rpx 30rpx;
	font-size: 28rpx;
}

.student-list {
	padding: 20rpx;
}

.student-item-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.student-avatar {
	width: 80rpx;
	height: 80rpx;
	background: #e3f2fd;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
}

.student-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.student-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.student-info {
	font-size: 22rpx;
	color: #666;
	line-height: 1.4;
}

.balance-section {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-top: 8rpx;
}

.balance-label {
	font-size: 24rpx;
	color: #666;
}

.balance-amount {
	font-size: 28rpx;
	font-weight: 600;
	color: #4CAF50;
	
	&.low-balance {
		color: #f44336;
	}
}

.student-actions {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.action-btn {
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 20rpx;
	font-size: 22rpx;
	
	&.recharge {
		background: #ff9800;
		color: white;
	}
	
	&.edit {
		background: #2196F3;
		color: white;
	}
}
</style>
