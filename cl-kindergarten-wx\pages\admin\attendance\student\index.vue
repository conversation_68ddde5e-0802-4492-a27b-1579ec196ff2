<template>
	<view class="container">
		<!-- 导航栏 -->
		<view class="navbar">
			<button class="back-btn" @click="goBack">←</button>
			<text class="navbar-title">学生考勤管理</text>
		</view>

		<!-- 日期选择 -->
		<view class="date-selector">
			<view class="date-nav">
				<button class="date-btn" @click="changeDate(-1)">←</button>
				<text class="current-date">{{ currentDate }}</text>
				<button class="date-btn" @click="changeDate(1)">→</button>
			</view>
			<view class="date-stats">
				<text class="stat-item">总人数: <text class="stat-number">{{ totalStudents }}</text></text>
				<text class="stat-item">已到: <text class="stat-number present">{{ presentCount }}</text></text>
				<text class="stat-item">未到: <text class="stat-number absent">{{ absentCount }}</text></text>
			</view>
		</view>

		<!-- 班级筛选 -->
		<view class="class-filter">
			<button 
				v-for="(classItem, index) in classFilter" 
				:key="index"
				class="filter-btn"
				:class="{ active: selectedClass === classItem.value }"
				@click="filterByClass(classItem.value)"
			>
				{{ classItem.label }}
			</button>
		</view>

		<!-- 考勤列表 -->
		<view class="attendance-list">
			<view v-for="classGroup in filteredAttendanceData" :key="classGroup.className" class="class-section">
				<view class="class-title">{{ classGroup.className }} ({{ classGroup.students.length }}人)</view>
				
				<view v-for="student in classGroup.students" :key="student.id" class="student-attendance-card">
					<view class="student-avatar">👶</view>
					<view class="student-info">
						<text class="student-name">{{ student.name }}</text>
						<text class="student-details">学号: {{ student.studentNo }}</text>
					</view>
					<view class="attendance-time">
						<text v-if="student.checkInTime" class="check-in">入园: {{ student.checkInTime }}</text>
						<text v-else class="absent-reason">未到园</text>
						<text v-if="student.checkOutTime" class="check-out">离园: {{ student.checkOutTime }}</text>
						<text v-else-if="student.checkInTime" class="check-out">离园: --:--</text>
					</view>
					<view class="attendance-actions">
						<view class="status" :class="student.status">
							{{ getStatusText(student.status) }}
						</view>
						<button class="action-btn" @click="editAttendance(student)">编辑</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 批量操作 -->
		<view class="batch-operations">
			<button class="batch-btn" @click="batchCheckIn">批量签到</button>
			<button class="batch-btn" @click="batchCheckOut">批量签退</button>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'

export default {
	data() {
		return {
			currentDate: '',
			selectedClass: 'all',
			totalStudents: 75,
			presentCount: 68,
			absentCount: 7,
			classFilter: [
				{ label: '全部', value: 'all' },
				{ label: '小班', value: 'small' },
				{ label: '中班', value: 'medium' },
				{ label: '大班', value: 'large' }
			],
			attendanceData: [
				{
					className: '小班',
					classValue: 'small',
					students: [
						{
							id: 1,
							name: '张小明',
							studentNo: '2024001',
							checkInTime: '08:15',
							checkOutTime: '',
							status: 'present'
						},
						{
							id: 2,
							name: '李小红',
							studentNo: '2024002',
							checkInTime: '08:20',
							checkOutTime: '',
							status: 'present'
						},
						{
							id: 3,
							name: '王小华',
							studentNo: '2024003',
							checkInTime: '',
							checkOutTime: '',
							status: 'absent'
						}
					]
				},
				{
					className: '中班',
					classValue: 'medium',
					students: [
						{
							id: 4,
							name: '赵小美',
							studentNo: '2024025',
							checkInTime: '',
							checkOutTime: '',
							status: 'leave',
							leaveReason: '病假 - 发烧'
						}
					]
				},
				{
					className: '大班',
					classValue: 'large',
					students: [
						{
							id: 5,
							name: '孙小强',
							studentNo: '2024050',
							checkInTime: '07:45',
							checkOutTime: '',
							status: 'present'
						}
					]
				}
			]
		}
	},
	computed: {
		filteredAttendanceData() {
			if (this.selectedClass === 'all') {
				return this.attendanceData
			}
			return this.attendanceData.filter(classGroup => classGroup.classValue === this.selectedClass)
		}
	},
	onLoad() {
		this.initCurrentDate()
	},
	methods: {
		initCurrentDate() {
			const now = new Date()
			const year = now.getFullYear()
			const month = String(now.getMonth() + 1).padStart(2, '0')
			const day = String(now.getDate()).padStart(2, '0')
			const weekdays = ['日', '一', '二', '三', '四', '五', '六']
			const weekday = weekdays[now.getDay()]
			this.currentDate = `${year}年${month}月${day}日 周${weekday}`
		},
		
		goBack() {
			uni.navigateBack()
		},
		
		changeDate(direction) {
			toast(`切换到${direction > 0 ? '下' : '上'}一天`)
		},
		
		filterByClass(classValue) {
			this.selectedClass = classValue
		},
		
		getStatusText(status) {
			const statusMap = {
				present: '已到园',
				absent: '未到园',
				leave: '请假'
			}
			return statusMap[status] || '未知'
		},
		
		editAttendance(student) {
			toast(`编辑 ${student.name} 的考勤记录`)
		},
		
		batchCheckIn() {
			uni.showModal({
				title: '批量签到',
				content: '确定要批量签到吗？',
				success: (res) => {
					if (res.confirm) {
						toast('批量签到操作完成')
					}
				}
			})
		},
		
		batchCheckOut() {
			uni.showModal({
				title: '批量签退',
				content: '确定要批量签退吗？',
				success: (res) => {
					if (res.confirm) {
						toast('批量签退操作完成')
					}
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

.navbar {
	background: white;
	padding: 20rpx 40rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.back-btn {
	background: none;
	border: none;
	font-size: 36rpx;
	color: #333;
	margin-right: 20rpx;
}

.navbar-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.date-selector {
	background: white;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.date-nav {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.date-btn {
	background: #f5f5f5;
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 20rpx;
	font-size: 24rpx;
}

.current-date {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.date-stats {
	display: flex;
	justify-content: space-around;
}

.stat-item {
	font-size: 24rpx;
	color: #666;
}

.stat-number {
	font-weight: 600;
	
	&.present {
		color: #4CAF50;
	}
	
	&.absent {
		color: #f44336;
	}
}

.class-filter {
	background: white;
	padding: 20rpx 40rpx;
	display: flex;
	gap: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.filter-btn {
	background: #f5f5f5;
	border: none;
	border-radius: 40rpx;
	padding: 12rpx 24rpx;
	font-size: 24rpx;
	color: #666;
	
	&.active {
		background: #4CAF50;
		color: white;
	}
}

.attendance-list {
	padding: 20rpx;
}

.class-section {
	margin-bottom: 40rpx;
}

.class-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
	padding-left: 20rpx;
	border-left: 6rpx solid #4CAF50;
}

.student-attendance-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.student-avatar {
	width: 60rpx;
	height: 60rpx;
	background: #e3f2fd;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
}

.student-info {
	min-width: 160rpx;
}

.student-name {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
}

.student-details {
	display: block;
	font-size: 22rpx;
	color: #666;
}

.attendance-time {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.check-in {
	font-size: 22rpx;
	color: #4CAF50;
}

.check-out {
	font-size: 22rpx;
	color: #ff9800;
}

.absent-reason {
	font-size: 22rpx;
	color: #f44336;
}

.attendance-actions {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 12rpx;
}

.status {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 20rpx;
	font-weight: 600;
	
	&.present {
		background: #e8f5e8;
		color: #4CAF50;
	}
	
	&.absent {
		background: #ffebee;
		color: #f44336;
	}
	
	&.leave {
		background: #fff3e0;
		color: #ff9800;
	}
}

.action-btn {
	background: #f5f5f5;
	border: none;
	border-radius: 8rpx;
	padding: 8rpx 16rpx;
	font-size: 22rpx;
	color: #666;
}

.batch-operations {
	position: fixed;
	bottom: 40rpx;
	left: 50%;
	transform: translateX(-50%);
	width: calc(100% - 80rpx);
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.15);
	display: flex;
	justify-content: space-around;
	gap: 20rpx;
}

.batch-btn {
	background: #4CAF50;
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 20rpx 40rpx;
	font-size: 24rpx;
	font-weight: 600;
	flex: 1;
}
</style>
