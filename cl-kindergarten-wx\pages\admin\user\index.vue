<template>
	<view class="container">
		<!-- 导航栏 -->
		<view class="navbar">
			<button class="back-btn" @click="goBack">←</button>
			<text class="navbar-title">用户管理</text>
			<button class="add-btn" @click="addUser">添加用户</button>
		</view>

		<!-- 角色筛选 -->
		<view class="role-filter">
			<button 
				v-for="(role, index) in roleFilter" 
				:key="index"
				class="filter-btn"
				:class="{ active: selectedRole === role.value }"
				@click="filterByRole(role.value)"
			>
				{{ role.label }}
			</button>
		</view>

		<!-- 用户列表 -->
		<view class="user-list">
			<view v-for="user in filteredUserList" :key="user.id" class="user-item-card">
				<view class="user-avatar">{{ user.roleIcon }}</view>
				<view class="user-details">
					<text class="user-name">{{ user.name }}</text>
					<text class="user-info">账号: {{ user.username }} | {{ user.roleName }}</text>
					<text class="user-info">联系电话: {{ user.phone }}</text>
					<text class="user-info">最后登录: {{ user.lastLogin }}</text>
					<view class="user-status">
						<text class="status-label">状态:</text>
						<text class="status-value" :class="user.status">
							{{ getStatusText(user.status) }}
						</text>
					</view>
				</view>
				<view class="user-actions">
					<button class="action-btn reset" @click="resetPassword(user)">重置密码</button>
					<button class="action-btn edit" @click="editUser(user)">编辑</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'

export default {
	data() {
		return {
			selectedRole: 'all',
			roleFilter: [
				{ label: '全部', value: 'all' },
				{ label: '管理员', value: 'admin' },
				{ label: '教师', value: 'teacher' },
				{ label: '家长', value: 'parent' }
			],
			userList: [
				{
					id: 1,
					name: '园长',
					username: 'admin',
					role: 'admin',
					roleName: '管理员',
					roleIcon: '👨‍💼',
					phone: '13800138000',
					lastLogin: '2024-11-30 09:15',
					status: 'active'
				},
				{
					id: 2,
					name: '张老师',
					username: 'teacher001',
					role: 'teacher',
					roleName: '教师',
					roleIcon: '👩‍🏫',
					phone: '13800138001',
					lastLogin: '2024-11-30 08:30',
					status: 'active'
				},
				{
					id: 3,
					name: '李老师',
					username: 'teacher002',
					role: 'teacher',
					roleName: '教师',
					roleIcon: '👨‍🏫',
					phone: '13800138002',
					lastLogin: '2024-11-29 17:45',
					status: 'active'
				},
				{
					id: 4,
					name: '张小明家长',
					username: 'parent001',
					role: 'parent',
					roleName: '家长',
					roleIcon: '👨‍👩‍👧‍👦',
					phone: '13800138101',
					lastLogin: '2024-11-29 20:30',
					status: 'active'
				},
				{
					id: 5,
					name: '李小红家长',
					username: 'parent002',
					role: 'parent',
					roleName: '家长',
					roleIcon: '👨‍👩‍👧‍👦',
					phone: '13800138102',
					lastLogin: '2024-11-28 19:15',
					status: 'inactive'
				}
			]
		}
	},
	computed: {
		filteredUserList() {
			if (this.selectedRole === 'all') {
				return this.userList
			}
			return this.userList.filter(user => user.role === this.selectedRole)
		}
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},
		
		addUser() {
			toast('添加用户功能开发中...')
			// useRouter('/pages/admin/user/add', {}, 'navigateTo')
		},
		
		filterByRole(role) {
			this.selectedRole = role
		},
		
		getStatusText(status) {
			const statusMap = {
				active: '正常',
				inactive: '禁用'
			}
			return statusMap[status] || '未知'
		},
		
		resetPassword(user) {
			uni.showModal({
				title: '重置密码',
				content: `确定要重置 ${user.name} 的密码吗？`,
				success: (res) => {
					if (res.confirm) {
						toast(`${user.name} 密码重置成功，新密码已发送`)
					}
				}
			})
		},
		
		editUser(user) {
			toast(`编辑用户: ${user.name}`)
			// useRouter('/pages/admin/user/edit', { id: user.id }, 'navigateTo')
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

.navbar {
	background: white;
	padding: 20rpx 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.back-btn {
	background: none;
	border: none;
	font-size: 36rpx;
	color: #333;
}

.navbar-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	flex: 1;
	text-align: center;
}

.add-btn {
	background: #4CAF50;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 24rpx;
	font-size: 24rpx;
}

.role-filter {
	background: white;
	padding: 20rpx 40rpx;
	display: flex;
	gap: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.filter-btn {
	background: #f5f5f5;
	border: none;
	border-radius: 40rpx;
	padding: 12rpx 24rpx;
	font-size: 24rpx;
	color: #666;
	
	&.active {
		background: #4CAF50;
		color: white;
	}
}

.user-list {
	padding: 20rpx;
}

.user-item-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.user-avatar {
	width: 80rpx;
	height: 80rpx;
	background: #e3f2fd;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
}

.user-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.user-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.user-info {
	font-size: 22rpx;
	color: #666;
	line-height: 1.4;
}

.user-status {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-top: 8rpx;
}

.status-label {
	font-size: 24rpx;
	color: #666;
}

.status-value {
	font-size: 24rpx;
	font-weight: 600;
	
	&.active {
		color: #4CAF50;
	}
	
	&.inactive {
		color: #f44336;
	}
}

.user-actions {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.action-btn {
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 20rpx;
	font-size: 22rpx;
	
	&.reset {
		background: #ff9800;
		color: white;
	}
	
	&.edit {
		background: #2196F3;
		color: white;
	}
}
</style>
