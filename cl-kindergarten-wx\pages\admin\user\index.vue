<template>
	<view class="container">
		<!-- 导航栏 -->
		<view class="navbar">
			<button class="back-btn" @click="goBack">←</button>
			<text class="navbar-title">用户管理</text>
			<button class="add-btn" @click="addUser">添加用户</button>
		</view>

		<!-- 角色筛选 -->
		<view class="role-filter">
			<button 
				v-for="(role, index) in roleFilter" 
				:key="index"
				class="filter-btn"
				:class="{ active: selectedRole === role.value }"
				@click="filterByRole(role.value)"
			>
				{{ role.label }}
			</button>
		</view>

		<!-- 用户列表 -->
		<view class="user-list">
			<view v-for="user in filteredUserList" :key="user.id" class="user-item-card">
				<view class="user-header">
					<view class="user-avatar" :class="user.role">{{ user.roleIcon }}</view>
					<view class="user-basic-info">
						<text class="user-name">{{ user.name }}</text>
						<view class="user-meta">
							<text class="role-badge" :class="user.role">{{ user.roleName }}</text>
							<text class="status-badge" :class="user.status">
								{{ getStatusText(user.status) }}
							</text>
						</view>
					</view>
					<view class="user-indicator">
						<text class="last-login-time">{{ formatLastLogin(user.lastLogin) }}</text>
					</view>
				</view>

				<view class="user-details">
					<view class="detail-row">
						<view class="detail-item">
							<text class="detail-label">👤 用户账号</text>
							<text class="detail-value">{{ user.username }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">📱 联系电话</text>
							<text class="detail-value">{{ user.phone }}</text>
						</view>
					</view>
					<view class="detail-row">
						<view class="detail-item">
							<text class="detail-label">🕐 最后登录</text>
							<text class="detail-value">{{ user.lastLogin }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">🔐 账户状态</text>
							<text class="detail-value" :class="user.status">{{ getStatusText(user.status) }}</text>
						</view>
					</view>
				</view>

				<view class="user-actions">
					<button class="action-btn primary" @click="editUser(user)">
						<text class="btn-icon">✏️</text>
						<text class="btn-text">编辑用户</text>
					</button>
					<button class="action-btn secondary" @click="resetPassword(user)">
						<text class="btn-icon">🔑</text>
						<text class="btn-text">重置密码</text>
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'

export default {
	data() {
		return {
			selectedRole: 'all',
			roleFilter: [
				{ label: '全部', value: 'all' },
				{ label: '管理员', value: 'admin' },
				{ label: '教师', value: 'teacher' },
				{ label: '家长', value: 'parent' }
			],
			userList: [
				{
					id: 1,
					name: '园长',
					username: 'admin',
					role: 'admin',
					roleName: '管理员',
					roleIcon: '👨‍💼',
					phone: '13800138000',
					lastLogin: '2024-11-30 09:15',
					status: 'active'
				},
				{
					id: 2,
					name: '张老师',
					username: 'teacher001',
					role: 'teacher',
					roleName: '教师',
					roleIcon: '👩‍🏫',
					phone: '13800138001',
					lastLogin: '2024-11-30 08:30',
					status: 'active'
				},
				{
					id: 3,
					name: '李老师',
					username: 'teacher002',
					role: 'teacher',
					roleName: '教师',
					roleIcon: '👨‍🏫',
					phone: '13800138002',
					lastLogin: '2024-11-29 17:45',
					status: 'active'
				},
				{
					id: 4,
					name: '张小明家长',
					username: 'parent001',
					role: 'parent',
					roleName: '家长',
					roleIcon: '👨‍👩‍👧‍👦',
					phone: '13800138101',
					lastLogin: '2024-11-29 20:30',
					status: 'active'
				},
				{
					id: 5,
					name: '李小红家长',
					username: 'parent002',
					role: 'parent',
					roleName: '家长',
					roleIcon: '👨‍👩‍👧‍👦',
					phone: '13800138102',
					lastLogin: '2024-11-28 19:15',
					status: 'inactive'
				}
			]
		}
	},
	computed: {
		filteredUserList() {
			if (this.selectedRole === 'all') {
				return this.userList
			}
			return this.userList.filter(user => user.role === this.selectedRole)
		}
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},
		
		addUser() {
			toast('添加用户功能开发中...')
			// useRouter('/pages/admin/user/add', {}, 'navigateTo')
		},
		
		filterByRole(role) {
			this.selectedRole = role
		},
		
		getStatusText(status) {
			const statusMap = {
				active: '正常',
				inactive: '禁用'
			}
			return statusMap[status] || '未知'
		},
		
		resetPassword(user) {
			uni.showModal({
				title: '重置密码',
				content: `确定要重置 ${user.name} 的密码吗？`,
				success: (res) => {
					if (res.confirm) {
						toast(`${user.name} 密码重置成功，新密码已发送`)
					}
				}
			})
		},
		
		editUser(user) {
			toast(`编辑用户: ${user.name}`)
			// useRouter('/pages/admin/user/edit', { id: user.id }, 'navigateTo')
		},

		// 格式化最后登录时间
		formatLastLogin(lastLogin) {
			const now = new Date()
			const loginTime = new Date(lastLogin)
			const diffMs = now - loginTime
			const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
			const diffDays = Math.floor(diffHours / 24)

			if (diffDays > 0) {
				return `${diffDays}天前`
			} else if (diffHours > 0) {
				return `${diffHours}小时前`
			} else {
				return '刚刚'
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
}

.navbar {
	background: white;
	padding: 20rpx 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
	position: sticky;
	top: 0;
	z-index: 100;
}

.back-btn {
	background: none;
	border: none;
	font-size: 36rpx;
	color: #333;
	padding: 8rpx;
	border-radius: 8rpx;
	transition: background 0.3s ease;

	&:active {
		background: #f5f5f5;
	}
}

.navbar-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	flex: 1;
	text-align: center;
}

.add-btn {
	background: linear-gradient(135deg, #4CAF50, #45a049);
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 16rpx 28rpx;
	font-size: 24rpx;
	font-weight: 600;
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
	}
}

.role-filter {
	background: white;
	padding: 24rpx 40rpx;
	display: flex;
	gap: 16rpx;
	border-bottom: 1rpx solid #f0f0f0;
	overflow-x: auto;
}

.filter-btn {
	background: #f5f5f5;
	border: none;
	border-radius: 24rpx;
	padding: 16rpx 28rpx;
	font-size: 24rpx;
	color: #666;
	white-space: nowrap;
	transition: all 0.3s ease;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);

	&.active {
		background: linear-gradient(135deg, #4CAF50, #45a049);
		color: white;
		box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
	}
}

.user-list {
	padding: 30rpx 20rpx;
}

.user-item-card {
	background: white;
	border-radius: 20rpx;
	padding: 0;
	margin-bottom: 24rpx;
	box-shadow: 0 8rpx 25rpx rgba(0,0,0,0.08);
	overflow: hidden;
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-4rpx);
		box-shadow: 0 12rpx 35rpx rgba(0,0,0,0.12);
	}
}

.user-header {
	padding: 24rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.user-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);

	&.admin {
		background: linear-gradient(135deg, #e3f2fd, #bbdefb);
	}

	&.teacher {
		background: linear-gradient(135deg, #fff3e0, #ffcc02);
	}

	&.parent {
		background: linear-gradient(135deg, #f3e5f5, #e1bee7);
	}
}

.user-basic-info {
	flex: 1;
}

.user-name {
	font-size: 36rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 8rpx;
}

.user-meta {
	display: flex;
	gap: 12rpx;
}

.role-badge {
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	font-weight: 600;

	&.admin {
		background: #e3f2fd;
		color: #1976d2;
	}

	&.teacher {
		background: #fff3e0;
		color: #f57c00;
	}

	&.parent {
		background: #f3e5f5;
		color: #7b1fa2;
	}
}

.status-badge {
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	font-weight: 600;

	&.active {
		background: #e8f5e8;
		color: #4CAF50;
	}

	&.inactive {
		background: #ffebee;
		color: #f44336;
	}
}

.user-indicator {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.last-login-time {
	font-size: 20rpx;
	color: #999;
	background: #f5f5f5;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

.user-details {
	padding: 24rpx;
	background: #fafafa;
}

.detail-row {
	display: flex;
	gap: 20rpx;
	margin-bottom: 16rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.detail-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 6rpx;
}

.detail-label {
	font-size: 22rpx;
	color: #666;
	font-weight: 500;
}

.detail-value {
	font-size: 26rpx;
	color: #333;
	font-weight: 600;

	&.active {
		color: #4CAF50;
	}

	&.inactive {
		color: #f44336;
	}
}

.user-actions {
	padding: 20rpx 24rpx;
	display: flex;
	gap: 16rpx;
	background: white;
}

.action-btn {
	flex: 1;
	border: none;
	border-radius: 12rpx;
	padding: 20rpx 16rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	transition: all 0.3s ease;

	&.primary {
		background: linear-gradient(135deg, #2196F3, #1976D2);
		color: white;
		box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.3);

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.3);
		}
	}

	&.secondary {
		background: linear-gradient(135deg, #ff9800, #f57c00);
		color: white;
		box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.3);

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 2rpx 8rpx rgba(255, 152, 0, 0.3);
		}
	}
}

.btn-icon {
	font-size: 24rpx;
}

.btn-text {
	font-size: 22rpx;
	font-weight: 600;
}
</style>
