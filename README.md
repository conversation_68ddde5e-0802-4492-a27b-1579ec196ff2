# 创联幼儿园核算系统 - 整合版演示

## 📱 项目介绍

这是一个完整的幼儿园核算系统微信小程序高保真原型，整合了所有功能模块到一个页面中，方便查看和演示。

## 🚀 快速开始

### 方式一：直接打开整合版
打开 `app.html` 文件即可查看完整的系统演示。

### 方式二：查看单独页面
- `index.html` - 登录入口页面
- `pages/employee/dashboard.html` - 员工工作台
- `pages/employee/attendance.html` - 考勤管理
- `pages/admin/dashboard.html` - 管理员后台

## 🎯 功能演示

### 登录测试账号

#### 员工账号
- 工号：`emp001` 密码：`123456` (张老师 - 小班班主任)
- 工号：`emp002` 密码：`123456` (李老师 - 副班主任)
- 手机：`13800138001` 密码：`123456` (王老师 - 托管教师)

#### 管理员账号
- 账号：`admin` 密码：`admin123` (园长)
- 账号：`finance` 密码：`123456` (财务主管)
- 手机：`13900139001` 密码：`123456` (教务主任)

## 📋 功能特性

### 🏠 登录页面
- ✅ 角色选择（员工/管理员）
- ✅ 用户登录验证
- ✅ 响应式设计
- ✅ 简洁大气的界面

### 👩‍🏫 员工端功能
- ✅ 个人信息展示
- ✅ 六大管理功能模块（完整实现）

### 👨‍💼 管理员端功能
- ✅ 个人信息展示
- ✅ 六大管理功能模块（完整实现）

### 📋 核心管理模块

#### 👶 学生管理
- ✅ 学生列表展示
- ✅ 预存款余额显示
- ✅ 预存款充值功能
- ✅ 搜索功能
- ✅ 编辑学生信息

#### 💰 园费管理
- ✅ 月度财务核算展示
- ✅ 收入/支出/净收益统计
- ✅ 重新核算按钮
- ✅ 学生账单列表
- ✅ 预存款自动扣除计算
- ✅ 月份切换功能
- ✅ 生成账单功能

#### 📚 课程管理
- ✅ 课程列表展示
- ✅ 课程信息管理
- ✅ 学员管理
- ✅ 添加/编辑课程

#### 👩‍🏫 教师管理
- ✅ 教师列表展示
- ✅ 工资信息显示
- ✅ 教师信息管理
- ✅ 工资详情查看

#### 📦 库存管理
- ✅ 物品分类管理
- ✅ 库存数量显示
- ✅ 低库存预警
- ✅ 入库/出库操作
- ✅ 分类筛选功能

#### 👥 用户管理
- ✅ 用户列表展示
- ✅ 角色分类筛选
- ✅ 用户信息管理
- ✅ 密码重置功能

## 🎨 设计特点

### 视觉设计
- **简洁大气**：采用卡片式设计，界面清爽简洁
- **功能导向**：专注核心管理功能，去除复杂统计
- **图标使用**：Emoji图标，直观易懂
- **响应式**：适配不同屏幕尺寸

### 交互体验
- **模块化设计**：六大核心管理模块
- **即时反馈**：操作提示和状态反馈
- **便捷导航**：简单的返回和跳转逻辑
- **功能完整**：以学生管理为例展示完整CRUD操作

## 🛠 技术实现

### 前端技术
- **HTML5**：语义化标签
- **CSS3**：Flexbox/Grid布局，动画效果
- **JavaScript ES6+**：模块化编程，异步处理
- **响应式设计**：移动优先设计

### 架构特点
- **单页应用**：SPA架构，无刷新切换
- **模块化**：功能模块分离，易于维护
- **组件化**：可复用的UI组件
- **数据驱动**：状态管理和数据绑定

## 📁 文件结构

```
├── app.html                 # 整合版主页面
├── index.html              # 登录入口页面
├── css/
│   ├── common.css          # 通用样式
│   ├── index.css           # 登录页样式
│   ├── employee.css        # 员工端样式
│   ├── admin.css           # 管理员端样式
│   └── app.css             # 整合版样式
├── js/
│   ├── common.js           # 通用功能
│   ├── index.js            # 登录页逻辑
│   ├── employee.js         # 员工端逻辑
│   ├── admin.js            # 管理员端逻辑
│   ├── attendance.js       # 考勤管理逻辑
│   └── app.js              # 整合版逻辑
└── pages/
    ├── employee/           # 员工端页面
    └── admin/              # 管理员端页面
```

## 🔧 使用说明

1. **打开演示**：直接用浏览器打开 `app.html`
2. **选择角色**：点击员工或管理员卡片
3. **登录系统**：使用测试账号登录
4. **体验功能**：在不同页面间切换体验功能
5. **查看数据**：所有数据都是模拟数据，用于演示

## 📱 移动端适配

- 支持手机、平板等移动设备
- 响应式布局自动适配屏幕
- 触摸友好的交互设计
- 优化的移动端性能

## 🎯 核心亮点

1. **完整业务逻辑**：实现了幼儿园核算的完整业务流程
2. **预存款机制**：每个学生都有预存款账户，支持充值和自动扣费
3. **财务核算功能**：月度财务统计、重新核算、账单生成
4. **六大管理模块**：用户、课程、园费、学生、教师、库存全覆盖
5. **真实业务场景**：基于实际幼儿园管理需求设计的功能模块

## 💡 业务逻辑说明

### 预存款机制
- 每个学生都有独立的预存款账户
- 家长可以提前充值，用于自动扣除各项费用
- 余额不足时会显示红色预警
- 支持余额查询和充值记录

### 财务核算流程
1. **月度核算**：系统自动计算每个学生的月度费用
2. **费用构成**：餐费（按出勤天数）+ 保教费（固定费用）
3. **自动扣费**：优先从预存款扣除，不足部分显示为待缴费
4. **重新核算**：支持重新计算整月财务数据
5. **账单生成**：一键生成所有学生的月度账单

### 核算规则
- **餐费计算**：出勤天数 × 20元/天
- **保教费**：出勤率≥50%收全费，<50%收半费
- **预存款扣除**：自动从预存款余额中扣除应缴费用
- **余额显示**：实时显示每个学生的预存款余额

## 📞 技术支持

如有问题或建议，请联系开发团队。

---

**© 2024 创联幼儿园核算系统 - 让幼儿园财务管理更简单、更高效**
