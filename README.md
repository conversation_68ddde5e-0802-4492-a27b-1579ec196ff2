# 创联幼儿园核算系统 - 整合版演示

## 📱 项目介绍

这是一个完整的幼儿园核算系统微信小程序高保真原型，整合了所有功能模块到一个页面中，方便查看和演示。

## 🚀 快速开始

### 方式一：直接打开整合版
打开 `app.html` 文件即可查看完整的系统演示。

### 方式二：查看单独页面
- `index.html` - 登录入口页面
- `pages/employee/dashboard.html` - 员工工作台
- `pages/employee/attendance.html` - 考勤管理
- `pages/admin/dashboard.html` - 管理员后台

## 🎯 功能演示

### 登录测试账号

#### 员工账号
- 工号：`emp001` 密码：`123456` (张老师 - 小班班主任)
- 工号：`emp002` 密码：`123456` (李老师 - 副班主任)
- 手机：`13800138001` 密码：`123456` (王老师 - 托管教师)

#### 管理员账号
- 账号：`admin` 密码：`admin123` (园长)
- 账号：`finance` 密码：`123456` (财务主管)
- 手机：`13900139001` 密码：`123456` (教务主任)

## 📋 功能特性

### 🏠 登录页面
- ✅ 角色选择（员工/管理员）
- ✅ 用户登录验证
- ✅ 响应式设计
- ✅ 简洁大气的界面

### 👩‍🏫 员工端功能
- ✅ 个人信息展示
- ✅ 管理功能模块
  - 👥 用户管理
  - 📚 课程管理
  - 💰 园费管理
  - 👶 学生管理（已实现详细页面）
  - 👩‍🏫 教师管理
  - 📦 库存管理

### 👨‍💼 管理员端功能
- ✅ 个人信息展示
- ✅ 管理功能模块
  - 👥 用户管理
  - 📚 课程管理
  - 💰 园费管理
  - 👶 学生管理（已实现详细页面）
  - 👩‍🏫 教师管理
  - 📦 库存管理

### 📋 学生管理功能（示例）
- ✅ 学生列表展示
- ✅ 搜索功能
- ✅ 添加学生
- ✅ 编辑学生信息
- ✅ 删除学生
- ✅ 返回导航

## 🎨 设计特点

### 视觉设计
- **简洁大气**：采用卡片式设计，界面清爽简洁
- **功能导向**：专注核心管理功能，去除复杂统计
- **图标使用**：Emoji图标，直观易懂
- **响应式**：适配不同屏幕尺寸

### 交互体验
- **模块化设计**：六大核心管理模块
- **即时反馈**：操作提示和状态反馈
- **便捷导航**：简单的返回和跳转逻辑
- **功能完整**：以学生管理为例展示完整CRUD操作

## 🛠 技术实现

### 前端技术
- **HTML5**：语义化标签
- **CSS3**：Flexbox/Grid布局，动画效果
- **JavaScript ES6+**：模块化编程，异步处理
- **响应式设计**：移动优先设计

### 架构特点
- **单页应用**：SPA架构，无刷新切换
- **模块化**：功能模块分离，易于维护
- **组件化**：可复用的UI组件
- **数据驱动**：状态管理和数据绑定

## 📁 文件结构

```
├── app.html                 # 整合版主页面
├── index.html              # 登录入口页面
├── css/
│   ├── common.css          # 通用样式
│   ├── index.css           # 登录页样式
│   ├── employee.css        # 员工端样式
│   ├── admin.css           # 管理员端样式
│   └── app.css             # 整合版样式
├── js/
│   ├── common.js           # 通用功能
│   ├── index.js            # 登录页逻辑
│   ├── employee.js         # 员工端逻辑
│   ├── admin.js            # 管理员端逻辑
│   ├── attendance.js       # 考勤管理逻辑
│   └── app.js              # 整合版逻辑
└── pages/
    ├── employee/           # 员工端页面
    └── admin/              # 管理员端页面
```

## 🔧 使用说明

1. **打开演示**：直接用浏览器打开 `app.html`
2. **选择角色**：点击员工或管理员卡片
3. **登录系统**：使用测试账号登录
4. **体验功能**：在不同页面间切换体验功能
5. **查看数据**：所有数据都是模拟数据，用于演示

## 📱 移动端适配

- 支持手机、平板等移动设备
- 响应式布局自动适配屏幕
- 触摸友好的交互设计
- 优化的移动端性能

## 🎯 核心亮点

1. **简洁实用**：专注核心管理功能，界面简洁大气
2. **模块化设计**：六大管理模块，功能清晰分离
3. **完整示例**：学生管理模块展示完整的CRUD操作
4. **易于扩展**：基于模块化架构，便于添加新功能
5. **用户友好**：直观的操作流程和即时反馈

## 📞 技术支持

如有问题或建议，请联系开发团队。

---

**© 2024 创联幼儿园核算系统 - 让幼儿园财务管理更简单、更高效**
