<template>
	<view class="container">
		<!-- 导航栏 -->
		<view class="navbar">
			<button class="back-btn" @click="goBack">←</button>
			<text class="navbar-title">用户协议</text>
		</view>

		<!-- 协议内容 -->
		<view class="content">
			<view class="header">
				<text class="title">创联幼儿园核算系统用户协议</text>
				<text class="update-time">最后更新时间：2024年11月30日</text>
			</view>

			<view class="agreement-content">
				<view class="section">
					<text class="section-title">欢迎使用创联幼儿园核算系统！</text>
					<text class="section-text">
						本协议是您与创联幼儿园核算系统之间关于使用本服务的法律协议。请您仔细阅读本协议的全部条款，特别是免除或者限制责任的条款。
					</text>
				</view>

				<view class="section">
					<text class="section-title">1. 服务条款</text>
					<text class="section-text">
						1.1 本协议是您与创联幼儿园核算系统之间关于使用本服务的法律协议。
					</text>
					<text class="section-text">
						1.2 您通过注册、登录、使用本服务，即表示您已阅读、理解并同意接受本协议的全部条款。
					</text>
					<text class="section-text">
						1.3 如果您不同意本协议的任何条款，请您立即停止使用本服务。
					</text>
				</view>

				<view class="section">
					<text class="section-title">2. 账户安全</text>
					<text class="section-text">
						2.1 您有责任保护您的账户信息安全，不得将账户信息泄露给他人。
					</text>
					<text class="section-text">
						2.2 您应当对您账户下的所有活动承担责任。
					</text>
					<text class="section-text">
						2.3 如发现账户被盗用，请立即联系我们的客服团队。
					</text>
				</view>

				<view class="section">
					<text class="section-title">3. 使用规范</text>
					<text class="section-text">
						3.1 您应当遵守相关法律法规，不得利用本服务从事违法违规活动。
					</text>
					<text class="section-text">
						3.2 不得恶意攻击、破坏系统安全或影响其他用户正常使用。
					</text>
					<text class="section-text">
						3.3 不得传播虚假信息或进行其他有害行为。
					</text>
				</view>

				<view class="section">
					<text class="section-title">4. 隐私保护</text>
					<text class="section-text">
						4.1 我们承诺保护您的个人信息安全，详见《隐私政策》。
					</text>
					<text class="section-text">
						4.2 我们采用行业标准的安全措施保护您的数据。
					</text>
				</view>

				<view class="section">
					<text class="section-title">5. 服务变更</text>
					<text class="section-text">
						5.1 我们保留随时修改或终止服务的权利，恕不另行通知。
					</text>
					<text class="section-text">
						5.2 我们会尽力提前通知重大变更。
					</text>
				</view>

				<view class="section">
					<text class="section-title">6. 免责声明</text>
					<text class="section-text">
						6.1 在法律允许的范围内，我们不承担因使用本服务而产生的任何损失。
					</text>
					<text class="section-text">
						6.2 我们不对第三方服务的可用性或准确性承担责任。
					</text>
				</view>

				<view class="section">
					<text class="section-title">7. 联系我们</text>
					<text class="section-text">
						如有疑问，请联系我们的客服团队：
					</text>
					<text class="section-text">
						邮箱：<EMAIL>
					</text>
					<text class="section-text">
						电话：400-123-4567
					</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	methods: {
		goBack() {
			uni.navigateBack()
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

.navbar {
	background: white;
	padding: 20rpx 40rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
	position: sticky;
	top: 0;
	z-index: 100;
}

.back-btn {
	background: none;
	border: none;
	font-size: 36rpx;
	color: #333;
	margin-right: 20rpx;
}

.navbar-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.content {
	padding: 40rpx;
}

.header {
	text-align: center;
	margin-bottom: 60rpx;
}

.title {
	display: block;
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.update-time {
	display: block;
	font-size: 24rpx;
	color: #666;
}

.agreement-content {
	background: white;
	border-radius: 16rpx;
	padding: 40rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.section {
	margin-bottom: 40rpx;
}

.section:last-child {
	margin-bottom: 0;
}

.section-title {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.section-text {
	display: block;
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
	margin-bottom: 16rpx;
}

.section-text:last-child {
	margin-bottom: 0;
}
</style>
