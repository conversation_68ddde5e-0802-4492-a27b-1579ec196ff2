<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员后台 - 创联幼儿园</title>
    <link rel="stylesheet" href="../../css/common.css">
    <link rel="stylesheet" href="../../css/admin.css">
</head>
<body>
    <div class="container">
        <!-- 头部信息栏 -->
        <div class="header">
            <div class="admin-info">
                <div class="avatar">👨‍💼</div>
                <div class="info">
                    <h2 id="adminName">园长</h2>
                    <p id="adminPosition">系统管理员</p>
                </div>
            </div>
            <div class="header-actions">
                <button class="header-btn" onclick="showNotifications()">🔔</button>
                <button class="header-btn" onclick="showSettings()">⚙️</button>
            </div>
        </div>

        <!-- 时间筛选 -->
        <div class="time-filter">
            <div class="filter-tabs">
                <button class="filter-tab active" data-period="month">本月</button>
                <button class="filter-tab" data-period="quarter">本季度</button>
                <button class="filter-tab" data-period="year">本年度</button>
            </div>
            <div class="date-range">
                <span id="currentPeriod">2024年11月</span>
            </div>
        </div>

        <!-- 财务概览 -->
        <div class="finance-overview">
            <div class="overview-card income">
                <div class="card-header">
                    <h3>总收入</h3>
                    <span class="trend up">📈 +12.5%</span>
                </div>
                <div class="card-amount">¥156,800</div>
                <div class="card-detail">
                    <span>园费: ¥98,400</span>
                    <span>托管费: ¥45,600</span>
                    <span>其他: ¥12,800</span>
                </div>
            </div>

            <div class="overview-card expense">
                <div class="card-header">
                    <h3>总支出</h3>
                    <span class="trend down">📉 -5.2%</span>
                </div>
                <div class="card-amount">¥89,200</div>
                <div class="card-detail">
                    <span>工资: ¥52,000</span>
                    <span>餐费: ¥18,500</span>
                    <span>运营: ¥18,700</span>
                </div>
            </div>

            <div class="overview-card profit">
                <div class="card-header">
                    <h3>净利润</h3>
                    <span class="trend up">📊 43.1%</span>
                </div>
                <div class="card-amount">¥67,600</div>
                <div class="card-detail">
                    <span>利润率: 43.1%</span>
                    <span>环比: +18.3%</span>
                </div>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="quick-actions">
            <h3 class="section-title">快捷操作</h3>
            <div class="action-grid">
                <a href="finance.html" class="action-item">
                    <div class="action-icon">💰</div>
                    <span>财务管理</span>
                </a>
                <a href="bills.html" class="action-item">
                    <div class="action-icon">📋</div>
                    <span>账单管理</span>
                </a>
                <a href="reports.html" class="action-item">
                    <div class="action-icon">📊</div>
                    <span>数据报表</span>
                </a>
                <a href="settings.html" class="action-item">
                    <div class="action-icon">⚙️</div>
                    <span>系统设置</span>
                </a>
            </div>
        </div>

        <!-- 收费统计 -->
        <div class="payment-stats">
            <h3 class="section-title">班级缴费统计</h3>
            <div class="stats-list">
                <div class="stats-item">
                    <div class="class-info">
                        <h4>小班</h4>
                        <p>40人</p>
                    </div>
                    <div class="payment-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 95%"></div>
                        </div>
                        <span class="progress-text">95% (38/40)</span>
                    </div>
                    <div class="payment-amount">¥59,280</div>
                </div>

                <div class="stats-item">
                    <div class="class-info">
                        <h4>中班</h4>
                        <p>38人</p>
                    </div>
                    <div class="payment-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 92%"></div>
                        </div>
                        <span class="progress-text">92% (35/38)</span>
                    </div>
                    <div class="payment-amount">¥54,600</div>
                </div>

                <div class="stats-item">
                    <div class="class-info">
                        <h4>大班</h4>
                        <p>36人</p>
                    </div>
                    <div class="payment-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 88%"></div>
                        </div>
                        <span class="progress-text">88% (32/36)</span>
                    </div>
                    <div class="payment-amount">¥49,920</div>
                </div>
            </div>
        </div>

        <!-- 最新动态 -->
        <div class="recent-activities">
            <h3 class="section-title">最新动态</h3>
            <div class="activity-list">
                <div class="activity-item">
                    <div class="activity-icon">💰</div>
                    <div class="activity-content">
                        <h4>张小明家长缴费</h4>
                        <p>园费 ¥1,560 - 刚刚</p>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon">📋</div>
                    <div class="activity-content">
                        <h4>11月账单已生成</h4>
                        <p>共114份账单 - 5分钟前</p>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon">👩‍🏫</div>
                    <div class="activity-content">
                        <h4>李老师提交考勤</h4>
                        <p>小班考勤已确认 - 10分钟前</p>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon">📊</div>
                    <div class="activity-content">
                        <h4>月度报表生成</h4>
                        <p>财务报表已更新 - 1小时前</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="chart-section">
            <h3 class="section-title">收入趋势</h3>
            <div class="chart-container">
                <div class="chart-placeholder">
                    <p>📈 收入趋势图</p>
                    <p>最近6个月收入变化</p>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="dashboard.html" class="nav-item active">
                <div class="nav-icon">🏠</div>
                <span>首页</span>
            </a>
            <a href="finance.html" class="nav-item">
                <div class="nav-icon">💰</div>
                <span>财务</span>
            </a>
            <a href="reports.html" class="nav-item">
                <div class="nav-icon">📊</div>
                <span>报表</span>
            </a>
            <a href="settings.html" class="nav-item">
                <div class="nav-icon">⚙️</div>
                <span>设置</span>
            </a>
            <a href="profile.html" class="nav-item">
                <div class="nav-icon">👤</div>
                <span>我的</span>
            </a>
        </div>
    </div>

    <script src="../../js/common.js"></script>
    <script src="../../js/admin.js"></script>
</body>
</html>
