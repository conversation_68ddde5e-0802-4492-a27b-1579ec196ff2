<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生考勤 - 创联幼儿园</title>
    <link rel="stylesheet" href="../../css/common.css">
    <link rel="stylesheet" href="../../css/employee.css">
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="navbar">
            <button class="back-btn" onclick="history.back()">←</button>
            <h1 class="navbar-title">学生考勤</h1>
            <button class="btn" id="batchBtn">批量操作</button>
        </div>

        <!-- 日期选择 -->
        <div class="date-selector">
            <div class="date-nav">
                <button class="date-btn" id="prevDate">←</button>
                <h3 id="currentDate">2024年11月30日 周六</h3>
                <button class="date-btn" id="nextDate">→</button>
            </div>
            <div class="date-stats">
                <span class="stat-item">总人数: <strong>25</strong></span>
                <span class="stat-item">已到: <strong>23</strong></span>
                <span class="stat-item">未到: <strong>2</strong></span>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="filter-section">
            <div class="filter-tabs">
                <button class="filter-tab active" data-status="all">全部</button>
                <button class="filter-tab" data-status="present">已到园</button>
                <button class="filter-tab" data-status="absent">未到园</button>
                <button class="filter-tab" data-status="leave">请假</button>
            </div>
        </div>

        <!-- 学生列表 -->
        <div class="student-attendance-list">
            <div class="student-card" data-student-id="1" data-status="present">
                <div class="student-checkbox">
                    <input type="checkbox" id="student1">
                </div>
                <div class="student-avatar">👶</div>
                <div class="student-info">
                    <h4>张小明</h4>
                    <p>学号: 2024001</p>
                    <div class="attendance-time">
                        <span class="check-in">入园: 08:15</span>
                        <span class="check-out">离园: --:--</span>
                    </div>
                </div>
                <div class="attendance-actions">
                    <div class="status-badge present">已到园</div>
                    <button class="action-btn" onclick="showAttendanceModal(1)">详情</button>
                </div>
            </div>

            <div class="student-card" data-student-id="2" data-status="present">
                <div class="student-checkbox">
                    <input type="checkbox" id="student2">
                </div>
                <div class="student-avatar">👶</div>
                <div class="student-info">
                    <h4>李小红</h4>
                    <p>学号: 2024002</p>
                    <div class="attendance-time">
                        <span class="check-in">入园: 08:20</span>
                        <span class="check-out">离园: --:--</span>
                    </div>
                </div>
                <div class="attendance-actions">
                    <div class="status-badge present">已到园</div>
                    <button class="action-btn" onclick="showAttendanceModal(2)">详情</button>
                </div>
            </div>

            <div class="student-card" data-student-id="3" data-status="absent">
                <div class="student-checkbox">
                    <input type="checkbox" id="student3">
                </div>
                <div class="student-avatar">👶</div>
                <div class="student-info">
                    <h4>王小华</h4>
                    <p>学号: 2024003</p>
                    <div class="attendance-time">
                        <span class="check-in">入园: --:--</span>
                        <span class="check-out">离园: --:--</span>
                    </div>
                </div>
                <div class="attendance-actions">
                    <div class="status-badge absent">未到园</div>
                    <button class="action-btn" onclick="showAttendanceModal(3)">签到</button>
                </div>
            </div>

            <div class="student-card" data-student-id="4" data-status="leave">
                <div class="student-checkbox">
                    <input type="checkbox" id="student4">
                </div>
                <div class="student-avatar">👶</div>
                <div class="student-info">
                    <h4>赵小美</h4>
                    <p>学号: 2024004</p>
                    <div class="attendance-time">
                        <span class="leave-reason">病假 - 发烧</span>
                    </div>
                </div>
                <div class="attendance-actions">
                    <div class="status-badge leave">病假</div>
                    <button class="action-btn" onclick="showAttendanceModal(4)">详情</button>
                </div>
            </div>
        </div>

        <!-- 快捷操作栏 -->
        <div class="quick-actions-bar">
            <button class="quick-btn" onclick="scanQRCode()">
                <span class="btn-icon">📱</span>
                扫码签到
            </button>
            <button class="quick-btn" onclick="manualAttendance()">
                <span class="btn-icon">✏️</span>
                手动考勤
            </button>
            <button class="quick-btn" onclick="exportData()">
                <span class="btn-icon">📊</span>
                导出数据
            </button>
        </div>

        <!-- 考勤详情弹窗 -->
        <div class="modal" id="attendanceModal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle">考勤详情</h3>
                    <button class="close-btn" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>学生姓名</label>
                        <input type="text" id="studentName" readonly>
                    </div>
                    <div class="form-group">
                        <label>考勤状态</label>
                        <select id="attendanceStatus">
                            <option value="present">出勤</option>
                            <option value="absent">缺勤</option>
                            <option value="late">迟到</option>
                            <option value="early">早退</option>
                            <option value="sick">病假</option>
                            <option value="personal">事假</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>入园时间</label>
                        <input type="time" id="checkInTime">
                    </div>
                    <div class="form-group">
                        <label>离园时间</label>
                        <input type="time" id="checkOutTime">
                    </div>
                    <div class="form-group">
                        <label>备注</label>
                        <textarea id="attendanceRemark" placeholder="请输入备注信息"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn" onclick="closeModal()">取消</button>
                    <button class="btn btn-primary" onclick="saveAttendance()">保存</button>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="dashboard.html" class="nav-item">
                <div class="nav-icon">🏠</div>
                <span>工作台</span>
            </a>
            <a href="attendance.html" class="nav-item active">
                <div class="nav-icon">📝</div>
                <span>考勤</span>
            </a>
            <a href="students.html" class="nav-item">
                <div class="nav-icon">👶</div>
                <span>学生</span>
            </a>
            <a href="salary.html" class="nav-item">
                <div class="nav-icon">💰</div>
                <span>工资</span>
            </a>
            <a href="profile.html" class="nav-item">
                <div class="nav-icon">👤</div>
                <span>我的</span>
            </a>
        </div>
    </div>

    <script src="../../js/common.js"></script>
    <script src="../../js/employee.js"></script>
    <script src="../../js/attendance.js"></script>
</body>
</html>
