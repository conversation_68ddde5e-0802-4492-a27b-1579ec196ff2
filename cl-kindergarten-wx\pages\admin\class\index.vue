<template>
	<view class="container">
		<!-- 导航栏 -->
		<view class="navbar">
			<button class="back-btn" @click="goBack">←</button>
			<text class="navbar-title">班级管理</text>
			<button class="add-btn" @click="addClass">添加班级</button>
		</view>

		<!-- 班级列表 -->
		<view class="class-list">
			<view v-for="classItem in classList" :key="classItem.id" class="class-item-card">
				<view class="class-icon">{{ classItem.icon }}</view>
				<view class="class-details">
					<text class="class-name">{{ classItem.name }}</text>
					<text class="class-info">班级代码: {{ classItem.code }} | 年龄段: {{ classItem.ageRange }}</text>
					<text class="class-info">班主任: {{ classItem.headTeacher }} | 副班主任: {{ classItem.assistantTeacher }}</text>
					<text class="class-info">学生人数: {{ classItem.currentStudents }}人 | 最大容量: {{ classItem.maxCapacity }}人</text>
					<text class="class-info">教室: {{ classItem.classroom }}</text>
				</view>
				<view class="class-actions">
					<button class="action-btn edit" @click="editClass(classItem)">编辑</button>
					<button class="action-btn view" @click="viewClassStudents(classItem)">学生</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'

export default {
	data() {
		return {
			classList: [
				{
					id: 1,
					name: '小班',
					code: 'XB001',
					ageRange: '3-4岁',
					headTeacher: '张老师',
					assistantTeacher: '刘老师',
					currentStudents: 25,
					maxCapacity: 30,
					classroom: '一楼101室',
					icon: '🎈'
				},
				{
					id: 2,
					name: '中班',
					code: 'ZB001',
					ageRange: '4-5岁',
					headTeacher: '李老师',
					assistantTeacher: '王老师',
					currentStudents: 28,
					maxCapacity: 30,
					classroom: '二楼201室',
					icon: '🎨'
				},
				{
					id: 3,
					name: '大班',
					code: 'DB001',
					ageRange: '5-6岁',
					headTeacher: '王老师',
					assistantTeacher: '陈老师',
					currentStudents: 22,
					maxCapacity: 25,
					classroom: '三楼301室',
					icon: '🎓'
				},
				{
					id: 4,
					name: '托管班',
					code: 'TG001',
					ageRange: '3-6岁',
					headTeacher: '赵老师',
					assistantTeacher: '孙老师',
					currentStudents: 15,
					maxCapacity: 20,
					classroom: '一楼102室',
					icon: '🌟'
				}
			]
		}
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},
		
		addClass() {
			toast('添加班级功能开发中...')
			// useRouter('/pages/admin/class/add', {}, 'navigateTo')
		},
		
		editClass(classItem) {
			toast(`编辑班级: ${classItem.name}`)
			// useRouter('/pages/admin/class/edit', { id: classItem.id }, 'navigateTo')
		},
		
		viewClassStudents(classItem) {
			toast(`查看 ${classItem.name} 的学生列表`)
			// useRouter('/pages/admin/class/students', { classId: classItem.id }, 'navigateTo')
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

.navbar {
	background: white;
	padding: 20rpx 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.back-btn {
	background: none;
	border: none;
	font-size: 36rpx;
	color: #333;
}

.navbar-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	flex: 1;
	text-align: center;
}

.add-btn {
	background: #4CAF50;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 24rpx;
	font-size: 24rpx;
}

.class-list {
	padding: 20rpx;
}

.class-item-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.class-icon {
	width: 80rpx;
	height: 80rpx;
	background: #e8f5e8;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 40rpx;
}

.class-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.class-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.class-info {
	font-size: 22rpx;
	color: #666;
	line-height: 1.4;
}

.class-actions {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.action-btn {
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 20rpx;
	font-size: 22rpx;
	
	&.edit {
		background: #2196F3;
		color: white;
	}
	
	&.view {
		background: #f5f5f5;
		color: #666;
	}
}
</style>
