<template>
	<view class="content">
		<u-cell-group title="帐号绑定:">
				<u-cell-item icon="account-fill" title="手机绑定" :value="baseInfo.phone" @click="phone"></u-cell-item>
				<u-cell-item icon="email-fill" title="邮箱绑定" :value="baseInfo.email" @click="email"></u-cell-item>
		</u-cell-group>
		
		<u-cell-group title="密码:">
				<u-cell-item icon="lock-fill" title="登陆密码" value="修改" @click="modifypassword"></u-cell-item>
		</u-cell-group>
		
		<u-cell-group title="帐号:">
				<u-cell-item icon="minus-people-fill" title="注销帐号" value="" @click="logout_account"></u-cell-item>
		</u-cell-group>
		
		<u-button @click="showModal=true" type="primary" style="margin-top: 20rpx;width:98%">
			  	退出登陆
		</u-button>
		
		
		<u-modal v-model="showModal" showCancelButton title="退出登录" content='确定要退出吗?' @cancel="showModal=false" @confirm="confirmExit"></u-modal>
		
	</view>
</template>
<script>
import {toast, clearStorageSync, setStorageSync, getStorageSync, useRouter} from '@/utils/utils.js'
export default {
	data() {
		return {
			baseInfo: {
			  name:'',
			  phone:'',
			  avatar:'',
			  email : '',
			  company_name:'',
			  registerDate:'',
			  login_code:'',
			  auth: 0,
			},
			version:"",
			showModal: false,
		}
	},
	onLoad(op) {
		//this.id  = op.id
	},
	onShow() {
	},
	onReady() {
		this.getUserInfo()
		const that = this
		uni.getSystemInfo({
			success: function (res) {
				console.log("res", res)
				that.version = res.appVersion
			}
		})
	},
	methods: {
		getUserInfo() {
			this.$api.baseInfo().then(res => {
				this.baseInfo = res.user
			})
		},
		confirmExit() {
			uni.removeStorageSync('token')
			this.showModal =false
			useRouter('/pages/public/login',{} ,'reLaunch')
		},
		
		modifypassword(){
			useRouter('/pages/my/account/password',{} ,'navigateTo')
		},
		email(){
			useRouter('/pages/my/account/email',{} ,'navigateTo')
		},
		phone(){
			useRouter('/pages/my/account/phone',{} ,'navigateTo')
		},
		logout_account(){
			useRouter('/pages/my/account/logout_account',{} ,'navigateTo')
		}
	}
}
</script>
<style lang="scss" scoped>	
    page{background:#f2f2f2}
	
</style>
