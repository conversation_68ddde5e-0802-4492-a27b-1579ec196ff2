<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="家长姓名" prop="parentName">
        <el-input
          v-model="queryParams.parentName"
          placeholder="请输入家长姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="微信绑定" prop="isWechatBound">
        <el-select v-model="queryParams.isWechatBound" placeholder="请选择绑定状态" clearable>
          <el-option label="已绑定" value="1" />
          <el-option label="未绑定" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['kg:parent:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['kg:parent:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['kg:parent:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['kg:parent:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="parentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="家长编号" align="center" prop="parentCode" width="120" />
      <el-table-column label="家长姓名" align="center" prop="parentName" width="100" />
      <el-table-column label="联系电话" align="center" prop="phone" width="120" />
      <el-table-column label="关系" align="center" prop="relationship" width="80">
        <template slot-scope="scope">
          {{ getRelationshipText(scope.row.relationship) }}
        </template>
      </el-table-column>
      <el-table-column label="学生信息" align="center" prop="studentName" width="200">
        <template slot-scope="scope">
          <div v-for="student in scope.row.students" :key="student.studentId" style="margin-bottom: 2px;">
            <el-tag size="mini" type="info">{{ student.studentName }} ({{ student.className }})</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="微信绑定" align="center" prop="isWechatBound" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isWechatBound === 1 ? 'success' : 'info'" size="mini">
            {{ scope.row.isWechatBound === 1 ? '已绑定' : '未绑定' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="绑定时间" align="center" prop="wechatBindTime" width="150" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['kg:parent:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['kg:parent:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['kg:parent:remove']"
          >删除</el-button>
          <el-button
            v-if="scope.row.isWechatBound === 0"
            size="mini"
            type="text"
            icon="el-icon-connection"
            @click="handleWechatBind(scope.row)"
            v-hasPermi="['kg:parent:wechat']"
          >微信绑定</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改家长信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="家长编号" prop="parentCode">
              <el-input v-model="form.parentCode" placeholder="请输入家长编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="家长姓名" prop="parentName">
              <el-input v-model="form.parentName" placeholder="请输入家长姓名" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="与学生关系" prop="relationship">
              <el-select v-model="form.relationship" placeholder="请选择关系" style="width: 100%">
                <el-option label="父亲" value="father" />
                <el-option label="母亲" value="mother" />
                <el-option label="爷爷" value="grandfather_paternal" />
                <el-option label="奶奶" value="grandmother_paternal" />
                <el-option label="外公" value="grandfather_maternal" />
                <el-option label="外婆" value="grandmother_maternal" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="form.idCard" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作单位" prop="workUnit">
              <el-input v-model="form.workUnit" placeholder="请输入工作单位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="家庭住址" prop="address">
              <el-input v-model="form.address" placeholder="请输入家庭住址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 微信绑定对话框 -->
    <el-dialog title="微信绑定" :visible.sync="wechatBindDialog" width="400px" append-to-body>
      <div style="text-align: center;">
        <p>请使用微信扫描下方二维码进行绑定</p>
        <div id="qrcode" style="margin: 20px auto; width: 200px; height: 200px; border: 1px solid #ddd;"></div>
        <p style="color: #999; font-size: 12px;">绑定成功后可接收学生在园信息推送</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="wechatBindDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listParent, getParent, delParent, addParent, updateParent } from "@/api/kg/parent/info";

export default {
  name: "ParentInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 家长信息表格数据
      parentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 微信绑定对话框
      wechatBindDialog: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        parentName: null,
        phone: null,
        studentName: null,
        isWechatBound: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        parentCode: [
          { required: true, message: "家长编号不能为空", trigger: "blur" }
        ],
        parentName: [
          { required: true, message: "家长姓名不能为空", trigger: "blur" }
        ],
        phone: [
          { required: true, message: "联系电话不能为空", trigger: "blur" },
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ],
        relationship: [
          { required: true, message: "请选择与学生关系", trigger: "change" }
        ],
        idCard: [
          { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: "请输入正确的身份证号", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询家长信息列表 */
    getList() {
      this.loading = true;
      listParent(this.queryParams).then(response => {
        this.parentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        parentId: null,
        parentCode: null,
        parentName: null,
        phone: null,
        relationship: null,
        idCard: null,
        workUnit: null,
        address: null,
        wechatOpenid: null,
        isWechatBound: 0,
        wechatBindTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.parentId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.generateParentCode();
      this.open = true;
      this.title = "添加家长信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const parentId = row.parentId || this.ids
      getParent(parentId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改家长信息";
      });
    },
    /** 查看详情 */
    handleDetail(row) {
      this.$router.push({
        path: '/kg/parent/info/detail',
        query: { parentId: row.parentId }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.parentId != null) {
            updateParent(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addParent(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const parentIds = row.parentId || this.ids;
      this.$modal.confirm('是否确认删除家长编号为"' + parentIds + '"的数据项？').then(function() {
        return delParent(parentIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('kg/parent/export', {
        ...this.queryParams
      }, `parent_${new Date().getTime()}.xlsx`)
    },
    /** 微信绑定 */
    handleWechatBind(row) {
      this.wechatBindDialog = true;
      // 这里应该生成微信绑定二维码
      this.$nextTick(() => {
        // 模拟二维码展示
        document.getElementById('qrcode').innerHTML = '<div style="line-height: 200px; text-align: center; color: #999;">二维码占位</div>';
      });
    },
    // 生成家长编号
    generateParentCode() {
      const now = new Date();
      const year = now.getFullYear().toString().substr(-2);
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
      this.form.parentCode = `PAR${year}${month}${random}`;
    },
    // 获取关系文本
    getRelationshipText(relationship) {
      const relationshipMap = {
        'father': '父亲',
        'mother': '母亲',
        'grandfather_paternal': '爷爷',
        'grandmother_paternal': '奶奶',
        'grandfather_maternal': '外公',
        'grandmother_maternal': '外婆',
        'other': '其他'
      };
      return relationshipMap[relationship] || '未知';
    }
  }
};
</script>
