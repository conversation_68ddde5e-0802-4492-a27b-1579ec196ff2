{
	// 如果您是通过uni_modules形式引入uView，可以忽略此配置
	"easycom": {
		"^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页"
			}
		},
		{
			"path": "pages/index/list",
			"style": {
				"navigationBarTitleText": "列表"
			}
		},
		{
			"path": "pages/my/index",
			"style": {
				"navigationBarTitleText": "我的",
				"navigationBarBackgroundColor": "#4790FF",
				"navigationBarTextStyle": "white",
				"enablePullDownRefresh": true

			}
		},
		{
			"path": "pages/public/login",
			"style": {
				"navigationBarTitleText": "管理员登录"
			}
		},
		{
			"path": "pages/public/wechat_login",
			"style": {
				"navigationBarTitleText": "微信登录"
			}
		},
		{
			"path" : "pages/my/profile",
			"style" : 
			{
				"navigationBarTitleText" : "个人信息",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/my/account/setting",
			"style" : 
			{
				"navigationBarTitleText" : "设置",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/my/account/password",
			"style" : 
			{
				"navigationBarTitleText" : "修改密码",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/my/account/index",
			"style" : 
			{
				"navigationBarTitleText" : "帐号&安全",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/my/about/index",
			"style" : 
			{
				"navigationBarTitleText" : "关于我们",
				"enablePullDownRefresh" : false
			}
		},

		{
			"path" : "pages/public/page",
			"style" : 
			{
				"navigationBarTitleText" : "",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/public/register",
			"style" : 
			{
				"navigationBarTitleText" : "注册",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/public/forget_password",
			"style" :
			{
				"navigationBarTitleText" : "忘记密码",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path": "pages/public/user_agreement",
			"style": {
				"navigationBarTitleText": "用户协议",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/public/privacy_policy",
			"style": {
				"navigationBarTitleText": "隐私政策",
				"navigationStyle": "custom"
			}
		},
		// 管理员页面
		{
			"path": "pages/admin/attendance/student/index",
			"style": {
				"navigationBarTitleText": "学生考勤管理",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/admin/attendance/teacher/index",
			"style": {
				"navigationBarTitleText": "教师考勤管理",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/admin/class/index",
			"style": {
				"navigationBarTitleText": "班级管理",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/admin/user/index",
			"style": {
				"navigationBarTitleText": "用户管理",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/admin/course/index",
			"style": {
				"navigationBarTitleText": "课程管理",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/admin/fee/index",
			"style": {
				"navigationBarTitleText": "园费管理",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/admin/student/index",
			"style": {
				"navigationBarTitleText": "学生管理",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/admin/teacher/index",
			"style": {
				"navigationBarTitleText": "教师管理",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/admin/inventory/index",
			"style": {
				"navigationBarTitleText": "库存管理",
				"navigationStyle": "custom"
			}
		}
    ],
	//分包加载配置，此配置为小程序的分包加载机制。
	"subPackages": [
		{
			"root": "pages/subPack", //子包的根目录
			"pages": [
				{
					"path": "index/detail",
					"style": {
						"navigationBarTitleText": "详情"
					}
				}
				
			]
		}
	],
	
	"globalStyle": {
		"navigationBarTextStyle": "white",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#2B8FFC",
		"backgroundColor": "#F8F8F8"
	}, 
	"tabBar": {
		"color": "#7A7E83",
		"selectedColor": "#2B8FFC",
		"borderStyle": "white",
		"list": [
			{
				"pagePath": "pages/index/index",
				"iconPath": "static/tab/home.png",
				"selectedIconPath": "static/tab/home_cur.png",
				"text": "首页"
			},
			// {
			// 	"pagePath": "pages/index/list",
			// 	"iconPath": "static/tab/list.png",
			// 	"selectedIconPath": "static/tab/list_cur.png",
			// 	"text": "列表"
			// },
			{
				"pagePath": "pages/my/index",
				"iconPath": "static/tab/my.png",
				"selectedIconPath": "static/tab/my_cur.png",
				"text": "我的"
			}
		]
	},
	
	// 分包预载配置
	"preloadRule": {
		// 当我们进入了pages/index/index页面以后就会预下载pages/subPack分包
		"pages/index/index": {
			"network": "all",	//在指定网络下预下载，可选值为：all（不限网络）、wifi（仅wifi下预下载）
			"packages": ["pages/subPack"]	//进入页面后预下载分包
		}
	},
	"uniIdRouter": {}
}
