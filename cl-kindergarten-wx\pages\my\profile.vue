<template>
	<view class="content">
		<u-cell-group title="个人信息:">
				<u-cell-item icon="account-fill" title="姓名" hover-class="none" :value="baseInfo.name" :arrow="false"></u-cell-item>
				<u-cell-item icon="phone-fill" title="手机" hover-class="none" :value="baseInfo.phone" :arrow="false"></u-cell-item>
		</u-cell-group>
		

	
		<u-cell-group title="其他信息:">
				<u-cell-item icon="home-fill" title="所属公司" hover-class="none" :value="baseInfo.company_name" :arrow="false"></u-cell-item>
				<u-cell-item icon="coupon-fill" title="标识号" hover-class="none" :value="baseInfo.login_code" :arrow="false"></u-cell-item>
				<u-cell-item icon="calendar-fill" title="注册时间" hover-class="none" :value="baseInfo.registerDate" :arrow="false"></u-cell-item>
		</u-cell-group>
	</view>
</template>
<script>
import {toast, clearStorageSync, setStorageSync, getStorageSync, useRouter} from '@/utils/utils.js'
export default {
	data() {
		return {
			baseInfo: {
			  name:'',
			  phone:'',
			  avatar:'',
			  company_name:'',
			  registerDate:'',
			  login_code:'',
			  auth: 0,
			  
			},
		}
	},
	onLoad(op) {
		//this.id  = op.id
	},
	onShow() {
	},
	onReady() {
		this.getUserInfo()
	},
	methods: {
		getUserInfo() {
			this.$api.baseInfo().then(res => {
				this.baseInfo = res.user
			})
		},
		auth(){
			if(!this.baseInfo.auth){
				useRouter('/pages/my/auth',{} ,'navigateTo')
			}else{
				toast('已实名认证')
			}
			
		},
		
	}
}
</script>
<style lang="scss" scoped>	
    page{background:#f2f2f2}
	
</style>
