<template>
	<view class="profile-container">
		<!-- 头像区域 -->
		<view class="avatar-section">
			<view class="avatar-wrapper" @click="changeAvatar">
				<u-avatar :src="baseInfo.avatar" size="160" mode="circle" class="avatar"></u-avatar>
				<view class="avatar-edit-icon">
					<u-icon name="camera" color="#fff" size="24"></u-icon>
				</view>
			</view>
			<text class="avatar-tip">点击更换头像</text>
		</view>

		<!-- 可编辑信息 -->
		<view class="form-section">
			<view class="section-title">
				<text class="title-text">可编辑信息</text>
			</view>
			<u-form :model="baseInfo" ref="uForm" :rules="rules" label-width="160">
				<u-form-item label="昵称" prop="nickName">
					<u-input
						v-model="baseInfo.nickName"
						placeholder="请输入昵称"
						:disabled="!isEditing"
						:border="isEditing"
						class="form-input"
					/>
				</u-form-item>

				<u-form-item label="手机号" prop="phone">
					<u-input
						v-model="baseInfo.phone"
						placeholder="请输入手机号"
						:disabled="!isEditing"
						:border="isEditing"
						class="form-input"
					/>
				</u-form-item>
			</u-form>
		</view>

		<!-- 基本信息（只读） -->
		<view class="info-section">
			<view class="section-title">
				<text class="title-text">基本信息</text>
			</view>
			<view class="info-list">
				<view class="info-item">
					<view class="info-label">用户名</view>
					<view class="info-value">{{baseInfo.userName || '未设置'}}</view>
				</view>

				<view class="info-item">
					<view class="info-label">注册时间</view>
					<view class="info-value">{{baseInfo.createTime || '未知'}}</view>
				</view>
			</view>
		</view>

		<!-- 操作按钮 -->
		<view class="action-section">
			<view v-if="!isEditing" class="edit-btn" @click="startEdit">
				<u-icon name="edit-pen" color="#fff" size="20"></u-icon>
				<text class="btn-text">编辑信息</text>
			</view>

			<view v-else class="edit-actions">
				<view class="cancel-btn" @click="cancelEdit">
					<text class="btn-text">取消</text>
				</view>
				<view class="save-btn" @click="saveInfo">
					<text class="btn-text">保存</text>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
import {toast, setStorageSync} from '@/utils/utils.js'

export default {
	data() {
		return {
			isEditing: false,
			originalInfo: {},
			baseInfo: {
				nickName: '',
				userName: '',
				phone: '',
				avatar: '',
				company_name: '',
				createTime: '',
				login_code: '',
				auth: 0
			},
			rules: {
				nickName: [
					{
						required: true,
						message: '请输入昵称',
						trigger: ['change', 'blur']
					}
				],
				phone: [
					{
						required: true,
						message: '请输入手机号',
						trigger: ['change', 'blur']
					},
					{
						pattern: /^1[3-9]\d{9}$/,
						message: '请输入正确的手机号',
						trigger: ['change', 'blur']
					}
				]
			}
		}
	},
	onLoad() {
		this.getUserInfo()
	},
	methods: {
		// 获取用户信息
		getUserInfo() {
			this.$api.baseInfo().then(res => {
				console.log('用户信息:', res)
				if (res.code === 200 || res.code === 0) {
					this.baseInfo = { ...res.user }
					this.originalInfo = { ...res.user }
				} else {
					toast('获取用户信息失败')
				}
			}).catch(err => {
				console.log('获取用户信息失败:', err)
				toast('获取用户信息失败')
			})
		},

		// 更换头像
		changeAvatar() {
			const _this = this
			uni.chooseImage({
				count: 1,
				sizeType: ['original', 'compressed'],
				sourceType: ['album', 'camera'],
				success: function (res) {
					uni.showLoading({ title: '上传中...' })

					uni.uploadFile({
						url: _this.$api_url + '/api/index/upload_cos',
						filePath: res.tempFilePaths[0],
						name: 'file',
						formData: {
							floder: 'avatar'
						},
						success: (uploadFileRes) => {
							uni.hideLoading()
							let retData = JSON.parse(uploadFileRes.data)
							console.log('头像上传结果:', retData)

							if (retData.code === 200 || retData.code === 0) {
								_this.baseInfo.avatar = retData.data.file
								_this.saveAvatarToServer(retData.data.file)
							} else {
								toast('头像上传失败')
							}
						},
						fail: () => {
							uni.hideLoading()
							toast('头像上传失败')
						}
					})
				}
			})
		},

		// 保存头像到服务器
		saveAvatarToServer(avatarUrl) {
			const data = {
				avatar: avatarUrl
			}
			this.$api.baseInfoSave(data).then(res => {
				if (res.code === 200 || res.code === 0) {
					toast('头像更新成功')
					// 更新本地存储的用户信息
					setStorageSync('admin_user', this.baseInfo)
				} else {
					toast(res.msg || '头像更新失败')
				}
			}).catch(err => {
				console.log('头像更新失败:', err)
				toast('头像更新失败')
			})
		},

		// 开始编辑
		startEdit() {
			this.isEditing = true
			this.originalInfo = { ...this.baseInfo }
		},

		// 取消编辑
		cancelEdit() {
			this.isEditing = false
			this.baseInfo = { ...this.originalInfo }
		},

		// 保存信息
		saveInfo() {
			this.$refs.uForm.validate(valid => {
				if (valid) {
					uni.showLoading({ title: '保存中...' })

					const data = {
						nickName: this.baseInfo.nickName,
						phone: this.baseInfo.phone
					}

					this.$api.baseInfoSave(data).then(res => {
						uni.hideLoading()
						if (res.code === 200 || res.code === 0) {
							toast('保存成功')
							this.isEditing = false
							// 更新本地存储的用户信息
							setStorageSync('admin_user', this.baseInfo)
						} else {
							toast(res.msg || '保存失败')
						}
					}).catch(err => {
						uni.hideLoading()
						console.log('保存失败:', err)
						toast('保存失败')
					})
				} else {
					toast('请检查输入信息')
				}
			})
		}
	}
}
</script>
<style lang="scss" scoped>
page {
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.profile-container {
	min-height: 100vh;
	padding: 30rpx;
}

/* 头像区域 */
.avatar-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 80rpx 0 60rpx;
	background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
	border-radius: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 6rpx;
		background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
	}
}

.avatar-wrapper {
	position: relative;
	margin-bottom: 24rpx;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
	}
}

.avatar {
	border: 6rpx solid #ffffff;
	box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.15);
	transition: all 0.3s ease;
}

.avatar-edit-icon {
	position: absolute;
	bottom: 12rpx;
	right: 12rpx;
	width: 56rpx;
	height: 56rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 4rpx solid #ffffff;
	box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.4);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
	}
}

.avatar-tip {
	font-size: 26rpx;
	color: #888888;
	font-weight: 500;
}

/* 区域标题 */
.section-title {
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.title-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	position: relative;

	&::before {
		content: '';
		position: absolute;
		left: -20rpx;
		top: 50%;
		transform: translateY(-50%);
		width: 8rpx;
		height: 32rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 4rpx;
	}
}

/* 表单区域 */
.form-section {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 40rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.form-input {
	&.disabled {
		color: #999999;
		background: #f8f9fa;
	}
}

/* 信息展示区域 */
.info-section {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.info-list {
	margin-top: 20rpx;
}

.info-item {
	display: flex;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #f5f5f5;

	&:last-child {
		border-bottom: none;
	}
}

.info-label {
	width: 160rpx;
	font-size: 30rpx;
	color: #666666;
	font-weight: 500;
}

.info-value {
	flex: 1;
	font-size: 30rpx;
	color: #333333;
	text-align: right;

	&:empty::after {
		content: '未设置';
		color: #cccccc;
	}
}

/* 操作按钮区域 */
.action-section {
	padding: 0 20rpx;
}

.edit-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 15rpx 35rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		transition: left 0.5s;
	}

	&:active {
		transform: translateY(2rpx) scale(0.98);
		box-shadow: 0 10rpx 25rpx rgba(102, 126, 234, 0.5);

		&::before {
			left: 100%;
		}
	}

	.btn-text {
		color: #ffffff;
		font-size: 32rpx;
		font-weight: 600;
		margin-left: 16rpx;
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
	}
}

.edit-actions {
	display: flex;
	gap: 30rpx;
}

.cancel-btn, .save-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	.btn-text {
		font-size: 32rpx;
		font-weight: 600;
	}

	&:active {
		transform: translateY(2rpx);
	}
}

.cancel-btn {
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border: 2rpx solid #dee2e6;
	box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(108, 117, 125, 0.1), transparent);
		transition: left 0.5s;
	}

	.btn-text {
		color: #6c757d;
		font-weight: 600;
		text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
	}

	&:active {
		transform: translateY(2rpx) scale(0.98);
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);

		&::before {
			left: 100%;
		}
	}
}

.save-btn {
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	box-shadow: 0 15rpx 35rpx rgba(40, 167, 69, 0.3);
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		transition: left 0.5s;
	}

	.btn-text {
		color: #ffffff;
		font-weight: 600;
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
	}

	&:active {
		transform: translateY(2rpx) scale(0.98);
		box-shadow: 0 10rpx 25rpx rgba(40, 167, 69, 0.5);

		&::before {
			left: 100%;
		}
	}
}

/* uView组件样式覆盖 */
::v-deep .u-form-item {
	margin-bottom: 30rpx;

	.u-form-item__body {
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.u-form-item__body__left__content__label {
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
	}
}

::v-deep .u-input {
	font-size: 30rpx;
	color: #333333;

	&.disabled {
		color: #999999 !important;
		background: #f8f9fa !important;
	}
}
</style>
