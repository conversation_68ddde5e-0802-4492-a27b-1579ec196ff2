<template>
	<view class="container">
		<!-- 导航栏 -->
		<view class="navbar">
			<button class="back-btn" @click="goBack">←</button>
			<text class="navbar-title">园费管理</text>
		</view>

		<!-- 月份选择 -->
		<view class="month-selector">
			<button class="month-btn" @click="changeMonth(-1)">←</button>
			<text class="current-month">{{ currentMonth }}</text>
			<button class="month-btn" @click="changeMonth(1)">→</button>
		</view>

		<!-- 重新核算按钮 -->
		<view class="recalculate-section">
			<button class="btn-recalculate" @click="recalculateFinance">
				🔄 重新核算本月财务
			</button>
		</view>

		<!-- 学生账单列表 -->
		<view class="bill-list">
			<view v-for="bill in billList" :key="bill.id" class="bill-item">
				<view class="student-info">
					<text class="student-name">{{ bill.studentName }}</text>
					<text class="student-class">{{ bill.class }}</text>
				</view>
				<view class="fee-details">
					<text class="fee-item">餐费: ¥{{ bill.mealFee.toFixed(2) }} ({{ bill.attendanceDays }}天)</text>
					<text class="fee-item">保教费: ¥{{ bill.tuitionFee.toFixed(2) }}</text>
					<text class="fee-total">应缴: ¥{{ bill.totalFee.toFixed(2) }}</text>
					<text class="prepaid-deduction">预存款扣除: ¥{{ bill.prepaidDeduction.toFixed(2) }}</text>
				</view>
				<view class="bill-status">
					<text class="remaining-amount" :class="{ 'paid': bill.remainingAmount === 0 }">
						{{ bill.remainingAmount === 0 ? '已缴费' : `待缴: ¥${bill.remainingAmount.toFixed(2)}` }}
					</text>
					<button class="action-btn" @click="generateBill(bill)">生成账单</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'

export default {
	data() {
		return {
			currentMonth: '2024年11月',
			billList: [
				{
					id: 1,
					studentName: '张小明',
					class: '小班',
					attendanceDays: 18,
					mealFee: 360.00,
					tuitionFee: 1200.00,
					totalFee: 1560.00,
					prepaidDeduction: 1200.00,
					remainingAmount: 360.00
				},
				{
					id: 2,
					studentName: '李小红',
					class: '小班',
					attendanceDays: 20,
					mealFee: 400.00,
					tuitionFee: 1200.00,
					totalFee: 1600.00,
					prepaidDeduction: 800.00,
					remainingAmount: 800.00
				},
				{
					id: 3,
					studentName: '王小华',
					class: '小班',
					attendanceDays: 15,
					mealFee: 300.00,
					tuitionFee: 600.00,
					totalFee: 900.00,
					prepaidDeduction: 150.00,
					remainingAmount: 750.00
				},
				{
					id: 4,
					studentName: '赵小美',
					class: '中班',
					attendanceDays: 19,
					mealFee: 380.00,
					tuitionFee: 1200.00,
					totalFee: 1580.00,
					prepaidDeduction: 1500.00,
					remainingAmount: 80.00
				},
				{
					id: 5,
					studentName: '孙小强',
					class: '大班',
					attendanceDays: 22,
					mealFee: 440.00,
					tuitionFee: 1200.00,
					totalFee: 1640.00,
					prepaidDeduction: 1640.00,
					remainingAmount: 0.00
				}
			]
		}
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},
		
		changeMonth(direction) {
			toast(`切换到${direction > 0 ? '下' : '上'}个月`)
		},
		
		recalculateFinance() {
			uni.showModal({
				title: '重新核算',
				content: '确定要重新核算本月财务吗？',
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: '核算中...'
						})
						
						setTimeout(() => {
							uni.hideLoading()
							toast('财务核算完成')
						}, 2000)
					}
				}
			})
		},
		
		generateBill(bill) {
			toast(`生成 ${bill.studentName} 的账单`)
			// 这里可以调用生成账单的API或跳转到账单详情页面
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

.navbar {
	background: white;
	padding: 20rpx 40rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.back-btn {
	background: none;
	border: none;
	font-size: 36rpx;
	color: #333;
	margin-right: 20rpx;
}

.navbar-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.month-selector {
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1rpx solid #f0f0f0;
}

.month-btn {
	background: #f5f5f5;
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 20rpx;
	font-size: 24rpx;
}

.current-month {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.recalculate-section {
	background: white;
	padding: 20rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.btn-recalculate {
	width: 100%;
	background: #4CAF50;
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 24rpx;
	font-size: 28rpx;
	font-weight: 600;
}

.bill-list {
	padding: 20rpx;
}

.bill-item {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.student-info {
	display: flex;
	align-items: center;
	gap: 20rpx;
	margin-bottom: 16rpx;
}

.student-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.student-class {
	background: #e3f2fd;
	color: #2196F3;
	padding: 4rpx 12rpx;
	border-radius: 20rpx;
	font-size: 20rpx;
}

.fee-details {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	margin-bottom: 16rpx;
}

.fee-item {
	font-size: 24rpx;
	color: #666;
}

.fee-total {
	font-size: 26rpx;
	font-weight: 600;
	color: #333;
}

.prepaid-deduction {
	font-size: 24rpx;
	color: #ff9800;
}

.bill-status {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.remaining-amount {
	font-size: 26rpx;
	font-weight: 600;
	color: #f44336;
	
	&.paid {
		color: #4CAF50;
	}
}

.action-btn {
	background: #2196F3;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 24rpx;
	font-size: 22rpx;
}
</style>
