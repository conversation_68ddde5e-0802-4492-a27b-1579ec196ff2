/* 通用样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 414px;
    margin: 0 auto;
    min-height: 100vh;
    background: #fff;
    position: relative;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    text-align: center;
    padding: 40px 20px 30px;
}

.logo {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.logo-icon {
    font-size: 60px;
    margin-bottom: 15px;
}

.title {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 8px;
}

.subtitle {
    font-size: 16px;
    opacity: 0.9;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 12px 24px;
    background: #4CAF50;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    border: none;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.btn-primary {
    background: #2196F3;
}

.btn-primary:hover {
    background: #1976D2;
}

.btn-danger {
    background: #f44336;
}

.btn-danger:hover {
    background: #d32f2f;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #4CAF50;
}

/* 导航栏样式 */
.navbar {
    background: white;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.navbar-title {
    font-size: 18px;
    font-weight: 600;
}

.back-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
}

/* 底部导航 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 414px;
    background: white;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-around;
    padding: 10px 0;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #666;
    font-size: 12px;
    padding: 5px;
    transition: color 0.3s ease;
}

.nav-item.active {
    color: #4CAF50;
}

.nav-icon {
    font-size: 20px;
    margin-bottom: 4px;
}

/* 工具类 */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.mt-20 {
    margin-top: 20px;
}

.mb-20 {
    margin-bottom: 20px;
}

.p-20 {
    padding: 20px;
}

.hidden {
    display: none;
}

/* 响应式 */
@media (max-width: 375px) {
    .container {
        max-width: 100%;
    }
    
    .header {
        padding: 30px 15px 25px;
    }
    
    .title {
        font-size: 24px;
    }
}
