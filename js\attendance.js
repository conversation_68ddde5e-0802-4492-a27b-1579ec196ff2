// 考勤管理JavaScript功能

class AttendancePage {
    constructor() {
        this.currentDate = new Date();
        this.selectedStudents = new Set();
        this.currentFilter = 'all';
        this.init();
    }

    init() {
        this.updateDateDisplay();
        this.loadAttendanceData();
        this.bindEvents();
    }

    // 更新日期显示
    updateDateDisplay() {
        const dateStr = Utils.formatDate(this.currentDate, 'YYYY年MM月DD日');
        const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
        const weekday = '周' + weekdays[this.currentDate.getDay()];
        
        const currentDateEl = document.getElementById('currentDate');
        if (currentDateEl) {
            currentDateEl.textContent = `${dateStr} ${weekday}`;
        }
    }

    // 加载考勤数据
    async loadAttendanceData() {
        try {
            // 模拟API调用
            const attendanceData = await this.fetchAttendanceData();
            this.updateStatistics(attendanceData);
            this.renderStudentList(attendanceData.students);
        } catch (error) {
            Toast.error('加载考勤数据失败');
        }
    }

    // 模拟获取考勤数据
    async fetchAttendanceData() {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    total: 25,
                    present: 23,
                    absent: 2,
                    leave: 1,
                    students: [
                        {
                            id: 1,
                            name: '张小明',
                            studentNo: '2024001',
                            status: 'present',
                            checkInTime: '08:15',
                            checkOutTime: null
                        },
                        {
                            id: 2,
                            name: '李小红',
                            studentNo: '2024002',
                            status: 'present',
                            checkInTime: '08:20',
                            checkOutTime: null
                        },
                        {
                            id: 3,
                            name: '王小华',
                            studentNo: '2024003',
                            status: 'absent',
                            checkInTime: null,
                            checkOutTime: null
                        },
                        {
                            id: 4,
                            name: '赵小美',
                            studentNo: '2024004',
                            status: 'leave',
                            leaveReason: '病假 - 发烧'
                        }
                    ]
                });
            }, 500);
        });
    }

    // 更新统计信息
    updateStatistics(data) {
        const statsEl = document.querySelector('.date-stats');
        if (statsEl) {
            statsEl.innerHTML = `
                <span class="stat-item">总人数: <strong>${data.total}</strong></span>
                <span class="stat-item">已到: <strong>${data.present}</strong></span>
                <span class="stat-item">未到: <strong>${data.absent}</strong></span>
            `;
        }
    }

    // 渲染学生列表
    renderStudentList(students) {
        const listContainer = document.querySelector('.student-attendance-list');
        if (!listContainer) return;

        listContainer.innerHTML = students.map(student => {
            return this.createStudentCard(student);
        }).join('');

        // 重新绑定事件
        this.bindStudentEvents();
    }

    // 创建学生卡片
    createStudentCard(student) {
        const statusMap = {
            present: { text: '已到园', class: 'present' },
            absent: { text: '未到园', class: 'absent' },
            leave: { text: '请假', class: 'leave' },
            late: { text: '迟到', class: 'late' },
            early: { text: '早退', class: 'early' }
        };

        const status = statusMap[student.status] || statusMap.absent;
        
        return `
            <div class="student-card" data-student-id="${student.id}" data-status="${student.status}">
                <div class="student-checkbox">
                    <input type="checkbox" id="student${student.id}">
                </div>
                <div class="student-avatar">👶</div>
                <div class="student-info">
                    <h4>${student.name}</h4>
                    <p>学号: ${student.studentNo}</p>
                    <div class="attendance-time">
                        ${student.status === 'leave' ? 
                            `<span class="leave-reason">${student.leaveReason}</span>` :
                            `<span class="check-in">入园: ${student.checkInTime || '--:--'}</span>
                             <span class="check-out">离园: ${student.checkOutTime || '--:--'}</span>`
                        }
                    </div>
                </div>
                <div class="attendance-actions">
                    <div class="status-badge ${status.class}">${status.text}</div>
                    <button class="action-btn" onclick="showAttendanceModal(${student.id})">
                        ${student.status === 'absent' ? '签到' : '详情'}
                    </button>
                </div>
            </div>
        `;
    }

    // 绑定事件
    bindEvents() {
        // 日期导航
        const prevDateBtn = document.getElementById('prevDate');
        const nextDateBtn = document.getElementById('nextDate');
        
        if (prevDateBtn) {
            prevDateBtn.addEventListener('click', () => this.changeDate(-1));
        }
        if (nextDateBtn) {
            nextDateBtn.addEventListener('click', () => this.changeDate(1));
        }

        // 筛选标签
        const filterTabs = document.querySelectorAll('.filter-tab');
        filterTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchFilter(e.target.dataset.status);
            });
        });

        // 批量操作
        const batchBtn = document.getElementById('batchBtn');
        if (batchBtn) {
            batchBtn.addEventListener('click', () => this.toggleBatchMode());
        }

        // 快捷操作
        this.bindQuickActions();
    }

    // 绑定学生相关事件
    bindStudentEvents() {
        // 复选框事件
        const checkboxes = document.querySelectorAll('.student-checkbox input');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const studentId = parseInt(e.target.id.replace('student', ''));
                if (e.target.checked) {
                    this.selectedStudents.add(studentId);
                } else {
                    this.selectedStudents.delete(studentId);
                }
                this.updateBatchButton();
            });
        });
    }

    // 绑定快捷操作
    bindQuickActions() {
        // 扫码签到
        window.scanQRCode = () => {
            Toast.info('启动扫码功能...');
            // 这里可以调用扫码API
        };

        // 手动考勤
        window.manualAttendance = () => {
            Toast.info('打开手动考勤界面...');
            // 这里可以显示手动考勤弹窗
        };

        // 导出数据
        window.exportData = () => {
            this.exportAttendanceData();
        };

        // 考勤详情弹窗
        window.showAttendanceModal = (studentId) => {
            this.showAttendanceModal(studentId);
        };

        // 关闭弹窗
        window.closeModal = () => {
            this.closeModal();
        };

        // 保存考勤
        window.saveAttendance = () => {
            this.saveAttendance();
        };
    }

    // 切换日期
    changeDate(days) {
        this.currentDate.setDate(this.currentDate.getDate() + days);
        this.updateDateDisplay();
        this.loadAttendanceData();
    }

    // 切换筛选
    switchFilter(status) {
        // 更新标签状态
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-status="${status}"]`).classList.add('active');

        this.currentFilter = status;
        this.filterStudentList();
    }

    // 筛选学生列表
    filterStudentList() {
        const studentCards = document.querySelectorAll('.student-card');
        studentCards.forEach(card => {
            const studentStatus = card.dataset.status;
            if (this.currentFilter === 'all' || studentStatus === this.currentFilter) {
                card.style.display = 'flex';
            } else {
                card.style.display = 'none';
            }
        });
    }

    // 切换批量模式
    toggleBatchMode() {
        const checkboxes = document.querySelectorAll('.student-checkbox');
        const isVisible = checkboxes[0].style.display !== 'none';
        
        checkboxes.forEach(checkbox => {
            checkbox.style.display = isVisible ? 'none' : 'block';
        });

        const batchBtn = document.getElementById('batchBtn');
        if (batchBtn) {
            batchBtn.textContent = isVisible ? '批量操作' : '取消批量';
        }

        if (isVisible) {
            this.selectedStudents.clear();
        }
    }

    // 更新批量按钮
    updateBatchButton() {
        const batchBtn = document.getElementById('batchBtn');
        if (batchBtn && this.selectedStudents.size > 0) {
            batchBtn.textContent = `已选择 ${this.selectedStudents.size} 人`;
        }
    }

    // 显示考勤详情弹窗
    showAttendanceModal(studentId) {
        const modal = document.getElementById('attendanceModal');
        if (!modal) return;

        // 获取学生信息
        const studentCard = document.querySelector(`[data-student-id="${studentId}"]`);
        const studentName = studentCard.querySelector('h4').textContent;
        const studentStatus = studentCard.dataset.status;

        // 填充表单
        document.getElementById('studentName').value = studentName;
        document.getElementById('attendanceStatus').value = studentStatus;

        // 显示弹窗
        modal.style.display = 'flex';
    }

    // 关闭弹窗
    closeModal() {
        const modal = document.getElementById('attendanceModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // 保存考勤
    async saveAttendance() {
        const studentName = document.getElementById('studentName').value;
        const status = document.getElementById('attendanceStatus').value;
        const checkInTime = document.getElementById('checkInTime').value;
        const checkOutTime = document.getElementById('checkOutTime').value;
        const remark = document.getElementById('attendanceRemark').value;

        try {
            // 模拟保存API调用
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            Toast.success('考勤记录保存成功');
            this.closeModal();
            this.loadAttendanceData(); // 重新加载数据
        } catch (error) {
            Toast.error('保存失败，请重试');
        }
    }

    // 导出考勤数据
    async exportAttendanceData() {
        Toast.info('正在导出考勤数据...');
        
        try {
            // 模拟导出过程
            await new Promise(resolve => setTimeout(resolve, 2000));
            Toast.success('考勤数据导出成功');
        } catch (error) {
            Toast.error('导出失败，请重试');
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在考勤页面
    if (window.location.pathname.includes('attendance.html')) {
        new AttendancePage();
    }
});
