<template>
  <view class="content">
	  <view class="head">
		  <u-avatar :src="baseInfo.avatar" size="140" mode="circle" class="avatar" :show-level="true" level-icon ="camera" @click="upload_avatar"></u-avatar>
		  <view class="personInfo">
			  <view>{{baseInfo.name}}</view>
			  <view class="account">帐号：{{baseInfo.phone}}</view>
			  
		  </view>	
	  </view>
	  
	<u-cell-group>
			<!-- <u-cell-item icon="account-fill" title="个人信息" @click="profile"></u-cell-item>
			<u-cell-item icon="lock-fill" title="帐号&安全" @click="account"></u-cell-item> -->
			<!-- 管理员入口 -->
			<u-cell-item icon="plus-people-fill" title="管理员菜单" @click="adminLogin" :border-bottom="false">
			</u-cell-item>

			<!-- <u-cell-item icon="info-circle" title="关于我们" @click="about"></u-cell-item> -->
	</u-cell-group>
	
	
	
	
	
		
		
  </view>
</template>

<script>
	import {toast, clearStorageSync, setStorageSync, getStorageSync, useRouter, checkUserLogin} from '@/utils/utils.js'
  export default {
    data(){
      return {
		  
		  baseInfo: {
			  name:'',
			  phone:'',
			  avatar:'',
			  company_name:'',
			  registerDate:'',
			  login_code:'',
			  auth: 0,
		  },
		  version:"",
      }
    },
	onLoad() {
		
		this.getUserInfo()
		const that = this
		uni.getSystemInfo({
			success: function (res) {
				console.log("res", res)
				that.version = res.appVersion
			}
		})
	}, 
	onShow() {
		uni.$on('refresh', e => {
		    this.getUserInfo()
		    uni.$off('refresh') 
		})
	},
	
	// 下拉刷新
	onPullDownRefresh() {
		//console.log('refresh');
		this.getUserInfo()
		uni.stopPullDownRefresh();//停止刷新
	},
	
	
    methods: {
		
		upload_avatar(){
			// 检查登录状态，需要登录才能上传头像
			checkUserLogin('上传头像', () => {
				const _this=this
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album','camera'], //从相册选择
					success: function (res) {

					uni.uploadFile({
						url: _this.$api_url + '/api/index/upload_cos', 
						filePath: res.tempFilePaths[0],
						name: 'file',
						formData: {
							floder: 'avatar'
						},
						success: (uploadFileRes) => {
							//let retData = JSON.parse(uploadFileRes)
						
							let retData = JSON.parse(uploadFileRes.data)
							console.log('up:',retData)
							const _data ={
								avatar: retData.data.file
							}
							_this.$api.baseInfoSave(_data).then(ret => {
								if (ret.code == 1) {
									_this.baseInfo.avatar = retData.data.file
								} else {
									toast(res.msg)	
								}
							}) 
							
						}
					});
					
					
					
				}
			});
		});
	},
		getUserInfo() {
			this.$api.baseInfo().then(res => {
				//console.log(res)
				this.baseInfo = res.data
				this.avatar_src= res.data.avatar
			})
		},
		
		service_center(){
			toast('未设置跳转，请自行定义')
		},
		
		profile(){
			// 检查登录状态，需要登录才能查看个人信息
			checkUserLogin('查看个人信息', () => {
				useRouter('/pages/my/profile',{} ,'navigateTo')
			});
		},
		account(){
			// 检查登录状态，需要登录才能访问账号安全
			checkUserLogin('访问账号安全', () => {
				useRouter('/pages/my/account/index',{} ,'navigateTo')
			});
		},
		setting(){
			useRouter('/pages/my/account/setting',{} ,'navigateTo')
		},
		about(){
			useRouter('/pages/my/about/index',{} ,'navigateTo')
		},
		adminLogin(){
			// 跳转到管理员登录页面
			useRouter('/pages/public/login',{} ,'navigateTo')
		},
    }
  }
</script>

<style lang="scss" scoped>
 .content{
	 
	 .head{
		 background-color:rgb(71, 144, 255);
		 display: flex;
		 
		 min-height: 300rpx;
		 padding-top: 60rpx;
		 .avatar{
			 margin-left: 50rpx;
		 }
		 .personInfo{
			 color:#fff;
			 margin-top: 25rpx;
			 font-size: 30rpx;
			 margin-left: 30rpx;
			 line-height: 50rpx;
			 .account{font-size: 26rpx;}
		 }
		 .auth{flex: 1;text-align: right;margin-top: 40rpx;}
		
	}
 }
</style>
