<template>
	<view class="container">
		<!-- 导航栏 -->
		<view class="navbar">
			<button class="back-btn" @click="goBack">←</button>
			<text class="navbar-title">教师考勤管理</text>
		</view>

		<!-- 日期选择 -->
		<view class="date-selector">
			<view class="date-nav">
				<button class="date-btn" @click="changeDate(-1)">←</button>
				<text class="current-date">{{ currentDate }}</text>
				<button class="date-btn" @click="changeDate(1)">→</button>
			</view>
			<view class="date-stats">
				<text class="stat-item">总人数: <text class="stat-number">{{ totalTeachers }}</text></text>
				<text class="stat-item">已签到: <text class="stat-number present">{{ presentCount }}</text></text>
				<text class="stat-item">未签到: <text class="stat-number absent">{{ absentCount }}</text></text>
			</view>
		</view>

		<!-- 教师考勤列表 -->
		<view class="attendance-list">
			<view v-for="teacher in teacherAttendanceData" :key="teacher.id" class="teacher-attendance-card">
				<view class="teacher-avatar">{{ teacher.gender === '男' ? '👨‍🏫' : '👩‍🏫' }}</view>
				<view class="teacher-info">
					<text class="teacher-name">{{ teacher.name }}</text>
					<text class="teacher-details">工号: {{ teacher.employeeNo }} | {{ teacher.position }}</text>
				</view>
				<view class="attendance-time">
					<text v-if="teacher.checkInTime" class="check-in">签到: {{ teacher.checkInTime }}</text>
					<text v-else-if="teacher.status === 'leave'" class="leave-reason">{{ teacher.leaveReason }}</text>
					<text v-else class="absent-reason">未签到</text>
					<text v-if="teacher.checkOutTime" class="check-out">签退: {{ teacher.checkOutTime }}</text>
					<text v-else-if="teacher.checkInTime" class="check-out">签退: --:--</text>
				</view>
				<view class="attendance-actions">
					<view class="status" :class="teacher.status">
						{{ getStatusText(teacher.status) }}
					</view>
					<button class="action-btn" @click="editAttendance(teacher)">编辑</button>
				</view>
			</view>
		</view>

		<!-- 批量操作 -->
		<view class="batch-operations">
			<button class="batch-btn" @click="batchCheckIn">批量签到</button>
			<button class="batch-btn" @click="batchCheckOut">批量签退</button>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'

export default {
	data() {
		return {
			currentDate: '',
			totalTeachers: 8,
			presentCount: 7,
			absentCount: 1,
			teacherAttendanceData: [
				{
					id: 1,
					name: '张老师',
					employeeNo: 'T001',
					position: '小班班主任',
					gender: '女',
					checkInTime: '08:00',
					checkOutTime: '',
					status: 'present'
				},
				{
					id: 2,
					name: '李老师',
					employeeNo: 'T002',
					position: '中班班主任',
					gender: '男',
					checkInTime: '07:55',
					checkOutTime: '',
					status: 'present'
				},
				{
					id: 3,
					name: '王老师',
					employeeNo: 'T003',
					position: '托管教师',
					gender: '女',
					checkInTime: '',
					checkOutTime: '',
					status: 'absent'
				},
				{
					id: 4,
					name: '赵老师',
					employeeNo: 'T004',
					position: '副班主任',
					gender: '女',
					checkInTime: '08:10',
					checkOutTime: '',
					status: 'present'
				},
				{
					id: 5,
					name: '孙老师',
					employeeNo: 'T005',
					position: '保育员',
					gender: '女',
					checkInTime: '',
					checkOutTime: '',
					status: 'leave',
					leaveReason: '事假'
				}
			]
		}
	},
	onLoad() {
		this.initCurrentDate()
	},
	methods: {
		initCurrentDate() {
			const now = new Date()
			const year = now.getFullYear()
			const month = String(now.getMonth() + 1).padStart(2, '0')
			const day = String(now.getDate()).padStart(2, '0')
			const weekdays = ['日', '一', '二', '三', '四', '五', '六']
			const weekday = weekdays[now.getDay()]
			this.currentDate = `${year}年${month}月${day}日 周${weekday}`
		},
		
		goBack() {
			uni.navigateBack()
		},
		
		changeDate(direction) {
			toast(`切换到${direction > 0 ? '下' : '上'}一天`)
		},
		
		getStatusText(status) {
			const statusMap = {
				present: '已签到',
				absent: '未签到',
				leave: '请假'
			}
			return statusMap[status] || '未知'
		},
		
		editAttendance(teacher) {
			toast(`编辑 ${teacher.name} 的考勤记录`)
		},
		
		batchCheckIn() {
			uni.showModal({
				title: '批量签到',
				content: '确定要批量签到吗？',
				success: (res) => {
					if (res.confirm) {
						toast('教师批量签到操作完成')
					}
				}
			})
		},
		
		batchCheckOut() {
			uni.showModal({
				title: '批量签退',
				content: '确定要批量签退吗？',
				success: (res) => {
					if (res.confirm) {
						toast('教师批量签退操作完成')
					}
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

.navbar {
	background: white;
	padding: 20rpx 40rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.back-btn {
	background: none;
	border: none;
	font-size: 36rpx;
	color: #333;
	margin-right: 20rpx;
}

.navbar-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.date-selector {
	background: white;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.date-nav {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.date-btn {
	background: #f5f5f5;
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 20rpx;
	font-size: 24rpx;
}

.current-date {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.date-stats {
	display: flex;
	justify-content: space-around;
}

.stat-item {
	font-size: 24rpx;
	color: #666;
}

.stat-number {
	font-weight: 600;
	
	&.present {
		color: #4CAF50;
	}
	
	&.absent {
		color: #f44336;
	}
}

.attendance-list {
	padding: 20rpx;
}

.teacher-attendance-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.teacher-avatar {
	width: 70rpx;
	height: 70rpx;
	background: #fff3e0;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
}

.teacher-info {
	min-width: 180rpx;
}

.teacher-name {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
}

.teacher-details {
	display: block;
	font-size: 22rpx;
	color: #666;
}

.attendance-time {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.check-in {
	font-size: 22rpx;
	color: #4CAF50;
}

.check-out {
	font-size: 22rpx;
	color: #ff9800;
}

.absent-reason, .leave-reason {
	font-size: 22rpx;
	color: #f44336;
}

.attendance-actions {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 12rpx;
}

.status {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 20rpx;
	font-weight: 600;
	
	&.present {
		background: #e8f5e8;
		color: #4CAF50;
	}
	
	&.absent {
		background: #ffebee;
		color: #f44336;
	}
	
	&.leave {
		background: #fff3e0;
		color: #ff9800;
	}
}

.action-btn {
	background: #f5f5f5;
	border: none;
	border-radius: 8rpx;
	padding: 8rpx 16rpx;
	font-size: 22rpx;
	color: #666;
}

.batch-operations {
	position: fixed;
	bottom: 40rpx;
	left: 50%;
	transform: translateX(-50%);
	width: calc(100% - 80rpx);
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.15);
	display: flex;
	justify-content: space-around;
	gap: 20rpx;
}

.batch-btn {
	background: #4CAF50;
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 20rpx 40rpx;
	font-size: 24rpx;
	font-weight: 600;
	flex: 1;
}
</style>
