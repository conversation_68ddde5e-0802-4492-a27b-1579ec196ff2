// 整合页面JavaScript功能

class App {
    constructor() {
        this.currentPage = 'loginPage';
        this.previousPage = null;
        this.selectedRole = null;
        this.currentUser = null;
        this.currentDate = new Date();
        this.init();
    }

    init() {
        this.bindEvents();
        this.showPage('loginPage');
        this.updateCurrentDate();
    }

    // 绑定事件
    bindEvents() {
        // 角色选择
        const roleCards = document.querySelectorAll('.role-card');
        roleCards.forEach(card => {
            card.addEventListener('click', (e) => {
                this.selectRole(e.currentTarget);
            });
        });

        // 登录按钮
        const loginBtn = document.getElementById('loginBtn');
        if (loginBtn) {
            loginBtn.addEventListener('click', () => {
                this.handleLogin();
            });
        }

        // 回车键登录
        const passwordInput = document.getElementById('password');
        if (passwordInput) {
            passwordInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleLogin();
                }
            });
        }

        // 绑定全局函数
        this.bindGlobalFunctions();
    }

    // 绑定全局函数
    bindGlobalFunctions() {
        // 页面导航函数
        window.showEmployeePage = () => this.showPage('employeePage');
        window.showAdminPage = () => this.showPage('adminPage');
        window.showLoginPage = () => this.showPage('loginPage');

        // 管理功能模块
        window.showUserManagement = () => this.showUserManagement();
        window.showCourseManagement = () => this.showCourseManagement();
        window.showTuitionManagement = () => this.showTuitionManagement();
        window.showStudentManagement = () => this.showStudentManagement();
        window.showTeacherManagement = () => this.showTeacherManagement();
        window.showInventoryManagement = () => this.showInventoryManagement();

        // 通用功能
        window.goBack = () => this.goBack();
        window.logout = () => this.logout();

        // 学生管理功能
        window.addStudent = () => this.addStudent();
        window.editStudent = (id) => this.editStudent(id);
        window.rechargeBalance = (id) => this.rechargeBalance(id);
        window.searchStudents = () => this.searchStudents();

        // 园费管理功能
        window.changeMonth = (direction) => this.changeMonth(direction);
        window.generateBills = () => this.generateBills();
        window.recalculateFinance = () => this.recalculateFinance();

        // 课程管理功能
        window.addCourse = () => this.addCourse();
        window.editCourse = (id) => this.editCourse(id);
        window.viewCourseStudents = (id) => this.viewCourseStudents(id);

        // 教师管理功能
        window.addTeacher = () => this.addTeacher();
        window.editTeacher = (id) => this.editTeacher(id);
        window.viewSalary = (id) => this.viewSalary(id);

        // 库存管理功能
        window.addInventory = () => this.addInventory();
        window.switchInventoryTab = (category) => this.switchInventoryTab(category);
        window.stockIn = (id) => this.stockIn(id);
        window.stockOut = (id) => this.stockOut(id);

        // 用户管理功能
        window.addUser = () => this.addUser();
        window.switchUserTab = (role) => this.switchUserTab(role);
        window.editUser = (id) => this.editUser(id);
        window.resetPassword = (id) => this.resetPassword(id);
    }

    // 显示各个管理页面
    showStudentManagement() {
        this.previousPage = this.currentPage;
        this.showPage('studentManagementPage');
        Toast.success('学生管理模块加载完成');
    }

    showTuitionManagement() {
        this.previousPage = this.currentPage;
        this.showPage('tuitionManagementPage');
        this.updateCurrentMonth();
        Toast.success('园费管理模块加载完成');
    }

    showCourseManagement() {
        this.previousPage = this.currentPage;
        this.showPage('courseManagementPage');
        Toast.success('课程管理模块加载完成');
    }

    showTeacherManagement() {
        this.previousPage = this.currentPage;
        this.showPage('teacherManagementPage');
        Toast.success('教师管理模块加载完成');
    }

    showInventoryManagement() {
        this.previousPage = this.currentPage;
        this.showPage('inventoryManagementPage');
        Toast.success('库存管理模块加载完成');
    }

    showUserManagement() {
        this.previousPage = this.currentPage;
        this.showPage('userManagementPage');
        Toast.success('用户管理模块加载完成');
    }

    // 返回上一页
    goBack() {
        if (this.previousPage) {
            this.showPage(this.previousPage);
        } else {
            // 根据当前用户角色返回对应首页
            if (this.selectedRole === 'employee') {
                this.showPage('employeePage');
            } else if (this.selectedRole === 'admin') {
                this.showPage('adminPage');
            }
        }
    }

    // 添加学生
    addStudent() {
        Toast.info('打开添加学生表单...');
        // 这里可以显示添加学生的弹窗或跳转到添加页面
    }

    // 编辑学生
    editStudent(studentId) {
        Toast.info(`编辑学生ID: ${studentId}`);
        // 这里可以显示编辑学生的弹窗或跳转到编辑页面
    }

    // 删除学生
    deleteStudent(studentId) {
        if (confirm('确定要删除这个学生吗？')) {
            Toast.success(`学生ID: ${studentId} 已删除`);
            // 这里可以调用删除API并刷新列表
        }
    }

    // 搜索学生
    searchStudents() {
        const searchInput = document.getElementById('studentSearch');
        const keyword = searchInput ? searchInput.value.trim() : '';

        if (keyword) {
            Toast.info(`搜索关键词: ${keyword}`);
            // 这里可以调用搜索API并更新列表
        } else {
            Toast.warning('请输入搜索关键词');
        }
    }

    // 充值预存款
    rechargeBalance(studentId) {
        const amount = prompt('请输入充值金额：');
        if (amount && !isNaN(amount) && parseFloat(amount) > 0) {
            Toast.success(`学生ID: ${studentId} 充值 ¥${amount} 成功`);
            // 这里可以调用充值API并刷新余额显示
        } else if (amount !== null) {
            Toast.error('请输入有效的充值金额');
        }
    }

    // 更新当前月份显示
    updateCurrentMonth() {
        const currentMonthEl = document.getElementById('currentMonth');
        if (currentMonthEl) {
            const now = new Date();
            currentMonthEl.textContent = `${now.getFullYear()}年${now.getMonth() + 1}月`;
        }
    }

    // 切换月份
    changeMonth(direction) {
        const currentMonthEl = document.getElementById('currentMonth');
        if (currentMonthEl) {
            // 这里可以实现月份切换逻辑
            Toast.info(`切换到${direction > 0 ? '下' : '上'}个月`);
        }
    }

    // 生成账单
    generateBills() {
        if (confirm('确定要生成本月账单吗？')) {
            Toast.info('正在生成账单...');
            setTimeout(() => {
                Toast.success('账单生成完成');
                // 这里可以刷新账单列表
            }, 2000);
        }
    }

    // 重新核算财务
    recalculateFinance() {
        if (confirm('确定要重新核算本月财务吗？此操作将重新计算所有学生的费用。')) {
            Toast.info('正在重新核算财务...');
            setTimeout(() => {
                Toast.success('财务核算完成');
                // 这里可以刷新财务数据
            }, 3000);
        }
    }

    // 课程管理功能
    addCourse() {
        Toast.info('打开添加课程表单...');
    }

    editCourse(courseId) {
        Toast.info(`编辑课程ID: ${courseId}`);
    }

    viewCourseStudents(courseId) {
        Toast.info(`查看课程ID: ${courseId} 的学员列表`);
    }

    // 教师管理功能
    addTeacher() {
        Toast.info('打开添加教师表单...');
    }

    editTeacher(teacherId) {
        Toast.info(`编辑教师ID: ${teacherId}`);
    }

    viewSalary(teacherId) {
        Toast.info(`查看教师ID: ${teacherId} 的工资详情`);
    }

    // 库存管理功能
    addInventory() {
        Toast.info('打开添加库存物品表单...');
    }

    switchInventoryTab(category) {
        // 更新标签状态
        document.querySelectorAll('.inventory-tabs .tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');

        // 筛选显示对应分类的物品
        const items = document.querySelectorAll('.inventory-item-card');
        items.forEach(item => {
            if (category === 'all' || item.dataset.category === category) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });

        Toast.info(`切换到${category === 'all' ? '全部' : category}分类`);
    }

    stockIn(itemId) {
        const quantity = prompt('请输入入库数量：');
        if (quantity && !isNaN(quantity) && parseInt(quantity) > 0) {
            Toast.success(`物品ID: ${itemId} 入库 ${quantity} 件成功`);
        } else if (quantity !== null) {
            Toast.error('请输入有效的数量');
        }
    }

    stockOut(itemId) {
        const quantity = prompt('请输入出库数量：');
        if (quantity && !isNaN(quantity) && parseInt(quantity) > 0) {
            Toast.success(`物品ID: ${itemId} 出库 ${quantity} 件成功`);
        } else if (quantity !== null) {
            Toast.error('请输入有效的数量');
        }
    }

    // 用户管理功能
    addUser() {
        Toast.info('打开添加用户表单...');
    }

    switchUserTab(role) {
        // 更新标签状态
        document.querySelectorAll('.user-tabs .tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');

        // 筛选显示对应角色的用户
        const users = document.querySelectorAll('.user-item-card');
        users.forEach(user => {
            if (role === 'all' || user.dataset.role === role) {
                user.style.display = 'flex';
            } else {
                user.style.display = 'none';
            }
        });

        Toast.info(`切换到${role === 'all' ? '全部' : role}用户`);
    }

    editUser(userId) {
        Toast.info(`编辑用户ID: ${userId}`);
    }

    resetPassword(userId) {
        if (confirm('确定要重置该用户的密码吗？')) {
            Toast.success(`用户ID: ${userId} 密码重置成功，新密码已发送`);
        }
    }

    // 选择角色
    selectRole(cardElement) {
        // 移除其他卡片的选中状态
        document.querySelectorAll('.role-card').forEach(card => {
            card.classList.remove('selected');
        });

        // 添加选中状态
        cardElement.classList.add('selected');

        // 获取角色类型
        this.selectedRole = cardElement.dataset.role;

        // 显示登录表单
        this.showLoginForm();
    }

    // 显示登录表单
    showLoginForm() {
        const loginSection = document.getElementById('loginSection');
        const loginTitle = document.getElementById('loginTitle');

        if (loginSection && loginTitle) {
            // 更新标题
            const roleNames = {
                employee: '员工登录',
                admin: '管理员登录'
            };
            loginTitle.textContent = roleNames[this.selectedRole] || '登录';

            // 显示登录区域
            loginSection.style.display = 'block';

            // 滚动到登录区域
            loginSection.scrollIntoView({ behavior: 'smooth' });

            // 聚焦到用户名输入框
            setTimeout(() => {
                const usernameInput = document.getElementById('username');
                if (usernameInput) {
                    usernameInput.focus();
                }
            }, 300);
        }
    }

    // 处理登录
    async handleLogin() {
        if (!this.selectedRole) {
            Toast.warning('请先选择身份');
            return;
        }

        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value.trim();

        // 表单验证
        if (!username) {
            Toast.warning('请输入工号或手机号');
            document.getElementById('username').focus();
            return;
        }

        if (!password) {
            Toast.warning('请输入密码');
            document.getElementById('password').focus();
            return;
        }

        // 显示加载状态
        this.setLoginLoading(true);

        try {
            // 模拟登录请求
            const response = await this.mockLogin(username, password, this.selectedRole);

            if (response.success) {
                Toast.success('登录成功');
                
                // 保存用户信息
                this.currentUser = response.data.user;
                Utils.setStorage('current_user', this.currentUser);
                Utils.setStorage('current_role', this.selectedRole);

                // 跳转到对应页面
                setTimeout(() => {
                    if (this.selectedRole === 'employee') {
                        this.showPage('employeePage');
                    } else if (this.selectedRole === 'admin') {
                        this.showPage('adminPage');
                    }
                    this.loadUserData();
                }, 1000);
            } else {
                Toast.error(response.message || '登录失败');
            }
        } catch (error) {
            Toast.error(error.message || '登录失败，请重试');
        } finally {
            this.setLoginLoading(false);
        }
    }

    // 模拟登录API
    async mockLogin(username, password, role) {
        return new Promise((resolve) => {
            setTimeout(() => {
                // 模拟登录验证
                const mockUsers = {
                    employee: {
                        'emp001': { password: '123456', name: '张老师', position: '小班班主任' },
                        'emp002': { password: '123456', name: '李老师', position: '副班主任' },
                        '13800138001': { password: '123456', name: '王老师', position: '托管教师' }
                    },
                    admin: {
                        'admin': { password: 'admin123', name: '园长', position: '园长' },
                        'finance': { password: '123456', name: '财务主管', position: '财务' },
                        '13900139001': { password: '123456', name: '教务主任', position: '教务主任' }
                    }
                };

                const roleUsers = mockUsers[role];
                const user = roleUsers && roleUsers[username];

                if (user && user.password === password) {
                    resolve({
                        success: true,
                        data: {
                            token: 'mock_token_' + Date.now(),
                            user: {
                                username,
                                name: user.name,
                                position: user.position,
                                role
                            }
                        }
                    });
                } else {
                    resolve({
                        success: false,
                        message: '用户名或密码错误'
                    });
                }
            }, 1000);
        });
    }

    // 设置登录按钮加载状态
    setLoginLoading(loading) {
        const loginBtn = document.getElementById('loginBtn');
        if (loginBtn) {
            if (loading) {
                loginBtn.disabled = true;
                loginBtn.innerHTML = '<span class="loading"></span> 登录中...';
            } else {
                loginBtn.disabled = false;
                loginBtn.innerHTML = '登录';
            }
        }
    }

    // 显示页面
    showPage(pageId) {
        // 隐藏所有页面
        document.querySelectorAll('.page').forEach(page => {
            page.style.display = 'none';
            page.classList.remove('active');
        });

        // 显示目标页面
        const targetPage = document.getElementById(pageId);
        if (targetPage) {
            targetPage.style.display = 'block';
            targetPage.classList.add('active');
            this.currentPage = pageId;
        }

        // 更新导航状态
        this.updateNavigation(pageId);
    }

    // 更新导航状态
    updateNavigation(pageId) {
        // 简化导航状态更新
        console.log(`当前页面: ${pageId}`);
    }

    // 加载用户数据
    loadUserData() {
        if (this.currentUser) {
            // 员工端数据
            const empUserNameEl = document.getElementById('empUserName');
            const empUserPositionEl = document.getElementById('empUserPosition');

            if (empUserNameEl) empUserNameEl.textContent = this.currentUser.name;
            if (empUserPositionEl) empUserPositionEl.textContent = this.currentUser.position;

            // 管理员端数据
            const adminNameEl = document.getElementById('adminName');
            const adminPositionEl = document.getElementById('adminPosition');

            if (adminNameEl) adminNameEl.textContent = this.currentUser.name;
            if (adminPositionEl) adminPositionEl.textContent = this.currentUser.position;
        }
    }

    // 登出
    logout() {
        Utils.clearStorage();
        this.currentUser = null;
        this.selectedRole = null;
        this.showPage('loginPage');
        Toast.info('已退出登录');
        
        // 重置登录表单
        document.getElementById('username').value = '';
        document.getElementById('password').value = '';
        document.getElementById('loginSection').style.display = 'none';
        document.querySelectorAll('.role-card').forEach(card => {
            card.classList.remove('selected');
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    new App();
});
