// 整合页面JavaScript功能

class App {
    constructor() {
        this.currentPage = 'loginPage';
        this.selectedRole = null;
        this.currentUser = null;
        this.currentDate = new Date();
        this.init();
    }

    init() {
        this.bindEvents();
        this.showPage('loginPage');
        this.updateCurrentDate();
    }

    // 绑定事件
    bindEvents() {
        // 角色选择
        const roleCards = document.querySelectorAll('.role-card');
        roleCards.forEach(card => {
            card.addEventListener('click', (e) => {
                this.selectRole(e.currentTarget);
            });
        });

        // 登录按钮
        const loginBtn = document.getElementById('loginBtn');
        if (loginBtn) {
            loginBtn.addEventListener('click', () => {
                this.handleLogin();
            });
        }

        // 回车键登录
        const passwordInput = document.getElementById('password');
        if (passwordInput) {
            passwordInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleLogin();
                }
            });
        }

        // 绑定全局函数
        this.bindGlobalFunctions();
    }

    // 绑定全局函数
    bindGlobalFunctions() {
        // 页面导航函数
        window.showEmployeePage = () => this.showPage('employeePage');
        window.showAdminPage = () => this.showPage('adminPage');
        window.showAttendancePage = () => this.showPage('attendancePage');
        window.showLoginPage = () => this.showPage('loginPage');

        // 员工端功能
        window.showCourseAttendance = () => Toast.info('托管考勤功能开发中...');
        window.showStatistics = () => Toast.info('考勤统计功能开发中...');
        window.showSalary = () => Toast.info('工资查询功能开发中...');
        window.showStudentsPage = () => Toast.info('学生管理功能开发中...');

        // 管理员端功能
        window.showFinancePage = () => Toast.info('财务管理功能开发中...');
        window.showBillsPage = () => Toast.info('账单管理功能开发中...');
        window.showReportsPage = () => Toast.info('数据报表功能开发中...');
        window.showSettingsPage = () => Toast.info('系统设置功能开发中...');
        window.showNotifications = () => Toast.info('通知功能开发中...');
        window.showSettings = () => Toast.info('设置功能开发中...');

        // 考勤功能
        window.toggleBatchMode = () => this.toggleBatchMode();
        window.changeDate = (days) => this.changeDate(days);
        window.showAttendanceDetail = (name) => Toast.info(`查看${name}的考勤详情`);
        window.markAttendance = (name) => this.markAttendance(name);
        window.scanQRCode = () => Toast.info('启动扫码功能...');
        window.exportData = () => this.exportData();

        // 通用功能
        window.logout = () => this.logout();
    }

    // 选择角色
    selectRole(cardElement) {
        // 移除其他卡片的选中状态
        document.querySelectorAll('.role-card').forEach(card => {
            card.classList.remove('selected');
        });

        // 添加选中状态
        cardElement.classList.add('selected');

        // 获取角色类型
        this.selectedRole = cardElement.dataset.role;

        // 显示登录表单
        this.showLoginForm();
    }

    // 显示登录表单
    showLoginForm() {
        const loginSection = document.getElementById('loginSection');
        const loginTitle = document.getElementById('loginTitle');

        if (loginSection && loginTitle) {
            // 更新标题
            const roleNames = {
                employee: '员工登录',
                admin: '管理员登录'
            };
            loginTitle.textContent = roleNames[this.selectedRole] || '登录';

            // 显示登录区域
            loginSection.style.display = 'block';

            // 滚动到登录区域
            loginSection.scrollIntoView({ behavior: 'smooth' });

            // 聚焦到用户名输入框
            setTimeout(() => {
                const usernameInput = document.getElementById('username');
                if (usernameInput) {
                    usernameInput.focus();
                }
            }, 300);
        }
    }

    // 处理登录
    async handleLogin() {
        if (!this.selectedRole) {
            Toast.warning('请先选择身份');
            return;
        }

        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value.trim();

        // 表单验证
        if (!username) {
            Toast.warning('请输入工号或手机号');
            document.getElementById('username').focus();
            return;
        }

        if (!password) {
            Toast.warning('请输入密码');
            document.getElementById('password').focus();
            return;
        }

        // 显示加载状态
        this.setLoginLoading(true);

        try {
            // 模拟登录请求
            const response = await this.mockLogin(username, password, this.selectedRole);

            if (response.success) {
                Toast.success('登录成功');
                
                // 保存用户信息
                this.currentUser = response.data.user;
                Utils.setStorage('current_user', this.currentUser);
                Utils.setStorage('current_role', this.selectedRole);

                // 跳转到对应页面
                setTimeout(() => {
                    if (this.selectedRole === 'employee') {
                        this.showPage('employeePage');
                        this.loadEmployeeData();
                    } else if (this.selectedRole === 'admin') {
                        this.showPage('adminPage');
                        this.loadAdminData();
                    }
                }, 1000);
            } else {
                Toast.error(response.message || '登录失败');
            }
        } catch (error) {
            Toast.error(error.message || '登录失败，请重试');
        } finally {
            this.setLoginLoading(false);
        }
    }

    // 模拟登录API
    async mockLogin(username, password, role) {
        return new Promise((resolve) => {
            setTimeout(() => {
                // 模拟登录验证
                const mockUsers = {
                    employee: {
                        'emp001': { password: '123456', name: '张老师', position: '小班班主任' },
                        'emp002': { password: '123456', name: '李老师', position: '副班主任' },
                        '13800138001': { password: '123456', name: '王老师', position: '托管教师' }
                    },
                    admin: {
                        'admin': { password: 'admin123', name: '园长', position: '园长' },
                        'finance': { password: '123456', name: '财务主管', position: '财务' },
                        '13900139001': { password: '123456', name: '教务主任', position: '教务主任' }
                    }
                };

                const roleUsers = mockUsers[role];
                const user = roleUsers && roleUsers[username];

                if (user && user.password === password) {
                    resolve({
                        success: true,
                        data: {
                            token: 'mock_token_' + Date.now(),
                            user: {
                                username,
                                name: user.name,
                                position: user.position,
                                role
                            }
                        }
                    });
                } else {
                    resolve({
                        success: false,
                        message: '用户名或密码错误'
                    });
                }
            }, 1000);
        });
    }

    // 设置登录按钮加载状态
    setLoginLoading(loading) {
        const loginBtn = document.getElementById('loginBtn');
        if (loginBtn) {
            if (loading) {
                loginBtn.disabled = true;
                loginBtn.innerHTML = '<span class="loading"></span> 登录中...';
            } else {
                loginBtn.disabled = false;
                loginBtn.innerHTML = '登录';
            }
        }
    }

    // 显示页面
    showPage(pageId) {
        // 隐藏所有页面
        document.querySelectorAll('.page').forEach(page => {
            page.style.display = 'none';
            page.classList.remove('active');
        });

        // 显示目标页面
        const targetPage = document.getElementById(pageId);
        if (targetPage) {
            targetPage.style.display = 'block';
            targetPage.classList.add('active');
            this.currentPage = pageId;
        }

        // 更新导航状态
        this.updateNavigation(pageId);
    }

    // 更新导航状态
    updateNavigation(pageId) {
        // 更新底部导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // 根据页面设置对应的导航项为活跃状态
        if (pageId === 'employeePage') {
            const workspaceNavs = document.querySelectorAll('.nav-item');
            if (workspaceNavs[0]) workspaceNavs[0].classList.add('active');
        } else if (pageId === 'attendancePage') {
            const attendanceNavs = document.querySelectorAll('.nav-item');
            if (attendanceNavs[1]) attendanceNavs[1].classList.add('active');
        } else if (pageId === 'adminPage') {
            const adminNavs = document.querySelectorAll('#adminPage .nav-item');
            if (adminNavs[0]) adminNavs[0].classList.add('active');
        }
    }

    // 加载员工数据
    loadEmployeeData() {
        if (this.currentUser) {
            const userNameEl = document.getElementById('empUserName');
            const userPositionEl = document.getElementById('empUserPosition');
            
            if (userNameEl) userNameEl.textContent = this.currentUser.name;
            if (userPositionEl) userPositionEl.textContent = this.currentUser.position;
        }
    }

    // 加载管理员数据
    loadAdminData() {
        if (this.currentUser) {
            const adminNameEl = document.getElementById('adminName');
            const adminPositionEl = document.getElementById('adminPosition');
            
            if (adminNameEl) adminNameEl.textContent = this.currentUser.name;
            if (adminPositionEl) adminPositionEl.textContent = this.currentUser.position;
        }
    }

    // 更新当前日期
    updateCurrentDate() {
        const dateStr = Utils.formatDate(this.currentDate, 'YYYY年MM月DD日');
        const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
        const weekday = '周' + weekdays[this.currentDate.getDay()];
        
        const empCurrentDateEl = document.getElementById('empCurrentDate');
        const attendanceCurrentDateEl = document.getElementById('attendanceCurrentDate');
        
        if (empCurrentDateEl) {
            empCurrentDateEl.textContent = dateStr;
        }
        if (attendanceCurrentDateEl) {
            attendanceCurrentDateEl.textContent = `${dateStr} ${weekday}`;
        }
    }

    // 切换批量模式
    toggleBatchMode() {
        Toast.info('批量操作模式切换');
    }

    // 切换日期
    changeDate(days) {
        this.currentDate.setDate(this.currentDate.getDate() + days);
        this.updateCurrentDate();
        Toast.info(`切换到 ${Utils.formatDate(this.currentDate, 'MM月DD日')}`);
    }

    // 标记考勤
    markAttendance(name) {
        Toast.success(`${name} 签到成功`);
        // 这里可以更新UI状态
    }

    // 导出数据
    async exportData() {
        Toast.info('正在导出数据...');
        
        try {
            // 模拟导出过程
            await new Promise(resolve => setTimeout(resolve, 2000));
            Toast.success('数据导出成功');
        } catch (error) {
            Toast.error('导出失败，请重试');
        }
    }

    // 登出
    logout() {
        Utils.clearStorage();
        this.currentUser = null;
        this.selectedRole = null;
        this.showPage('loginPage');
        Toast.info('已退出登录');
        
        // 重置登录表单
        document.getElementById('username').value = '';
        document.getElementById('password').value = '';
        document.getElementById('loginSection').style.display = 'none';
        document.querySelectorAll('.role-card').forEach(card => {
            card.classList.remove('selected');
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    new App();
});
