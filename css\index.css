/* 首页特定样式 */

/* 角色选择区域 */
.role-selection {
    padding: 40px 20px;
    text-align: center;
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 30px;
    color: #333;
}

.role-cards {
    display: flex;
    justify-content: space-around;
    gap: 20px;
    margin-bottom: 40px;
}

.role-card {
    flex: 1;
    background: white;
    border-radius: 16px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.role-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.role-card.selected {
    border-color: #4CAF50;
    background: #f8fff8;
}

.role-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.role-card h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.role-card p {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

/* 登录区域 */
.login-section {
    padding: 0 20px 40px;
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-form {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.login-form h3 {
    text-align: center;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 30px;
    color: #333;
}

.login-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.login-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.link {
    color: #4CAF50;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.link:hover {
    color: #45a049;
    text-decoration: underline;
}

/* 底部信息 */
.footer {
    text-align: center;
    padding: 30px 20px;
    color: #666;
    font-size: 12px;
    line-height: 1.5;
    background: #f8f9fa;
}

.footer p {
    margin-bottom: 5px;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 错误提示 */
.error-message {
    background: #ffebee;
    color: #c62828;
    padding: 10px 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    font-size: 14px;
    border-left: 4px solid #f44336;
}

.success-message {
    background: #e8f5e8;
    color: #2e7d32;
    padding: 10px 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    font-size: 14px;
    border-left: 4px solid #4CAF50;
}

/* 响应式调整 */
@media (max-width: 375px) {
    .role-cards {
        flex-direction: column;
        gap: 15px;
    }
    
    .role-card {
        padding: 25px 15px;
    }
    
    .role-icon {
        font-size: 40px;
    }
    
    .login-form {
        padding: 25px 20px;
    }
}
