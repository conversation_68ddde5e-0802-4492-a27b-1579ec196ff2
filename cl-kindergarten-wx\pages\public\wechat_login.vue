<template>
	<view class="login-page">
		<!-- 头部区域 -->
		<view class="header-section">
			<view class="logo">
				<image class="logo-image" src="/static/images/logo.png" alt="Logo" />
			</view>
			
			<view class="app-info">
				<text class="app-name">创联幼儿园核算系统</text>
				<text class="app-slogan">专业的幼儿园财务管理平台</text>
			</view>
		</view>

		<!-- 登录区域 -->
		<view class="login-section">
			<view class="welcome-text">
				<text class="title">欢迎登录</text>
				<text class="subtitle">请使用微信授权快速登录</text>
			</view>

			<!-- 微信登录按钮 -->
			<!-- #ifdef MP-WEIXIN -->
			<button
				class="wechat-login-btn"
				open-type="getPhoneNumber"
				@getphonenumber="getPhoneNumber">
				<u-icon name="weixin-fill" color="#fff" size="44"></u-icon>
				<text class="btn-text">微信一键登录</text>
			</button>
			<!-- #endif -->

			<!-- 用户协议 -->
			<view class="agreement-section">
				<text class="agreement-text">
					登录即表示同意
					<text class="link-text" @click="showUserAgreement">《用户协议》</text>
					和
					<text class="link-text" @click="showPrivacyPolicy">《隐私政策》</text>
				</text>
			</view>
		</view>

		<!-- 底部信息 -->
		<view class="footer-section">
			<text class="copyright">© 2024 创联幼儿园核算系统</text>
		</view>
	</view>
</template>

<script>
import {toast, setStorageSync, useRouter} from '@/utils/utils.js'

export default {
	data() {
		return {
			
		}
	},
	onLoad() {

	},
	methods: {
		
		// 微信授权获取手机号
		getPhoneNumber(e) {
			console.log('微信授权手机号', e);
			if (e.detail.errMsg === 'getPhoneNumber:ok') {
				// 将加密数据发送到后端解密
				const data = {
					encryptedData: e.detail.encryptedData,
					iv: e.detail.iv,
					code: e.detail.code // 用于获取session_key
				};

				uni.showLoading({title: '登录中...'});

				this.$api.wechatPhoneLogin(data).then(res => {
					uni.hideLoading();
					if (res.code === 0) {
						toast('登录成功');
						// 使用user_前缀存储用户token
						setStorageSync('user_token', res.data.token);
						// 同时保持原有的token存储，保证兼容性
						setStorageSync('token', res.data.token);

						// 触发登录成功事件
						uni.$emit('loginSuccess');

						// 延迟跳转，让用户看到成功提示
						setTimeout(() => {
							// 跳转到主页面
							useRouter('/pages/index/list', {}, 'switchTab');
						}, 1000);
					} else {
						toast(res.msg || '登录失败');
					}
				}).catch(err => {
					uni.hideLoading();
					toast('登录失败，请重试');
				});
			} else {
				toast('需要授权手机号才能登录');
			}
		},

		// 显示用户协议
		showUserAgreement() {
			useRouter('/pages/public/user_agreement', {}, 'navigateTo');
		},

		// 显示隐私政策
		showPrivacyPolicy() {
			useRouter('/pages/public/privacy_policy', {}, 'navigateTo');
		},
		

	}
}
</script>

<style lang="scss" scoped>
.login-page {
	min-height: 100vh;
	background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
	display: flex;
	flex-direction: column;
}

/* 头部区域 */
.header-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 120rpx 60rpx 80rpx;
	color: #fff;
}

.logo {
	margin-bottom: 60rpx;
	
	.logo-image {
		width: 160rpx;
		height: 160rpx;
		border-radius: 24rpx;
		box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.15);
	}
}

.app-info {
	text-align: center;
	
	.app-name {
		display: block;
		font-size: 48rpx;
		font-weight: 600;
		margin-bottom: 16rpx;
		letter-spacing: 1rpx;
	}
	
	.app-slogan {
		display: block;
		font-size: 28rpx;
		opacity: 0.9;
		font-weight: 400;
	}
}

/* 登录区域 */
.login-section {
	background: #fff;
	border-radius: 32rpx 32rpx 0 0;
	padding: 80rpx 60rpx 60rpx;
	box-shadow: 0 -4rpx 24rpx rgba(0, 0, 0, 0.08);
}

.welcome-text {
	text-align: center;
	margin-bottom: 80rpx;
	
	.title {
		display: block;
		font-size: 44rpx;
		font-weight: 600;
		color: #1a1a1a;
		margin-bottom: 16rpx;
	}
	
	.subtitle {
		display: block;
		font-size: 28rpx;
		color: #666;
		font-weight: 400;
	}
}

/* 微信登录按钮 */
.wechat-login-btn {
	width: 100%;
	height: 108rpx;
	background: #07c160;
	border: none;
	border-radius: 54rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 80rpx;
	box-shadow: 0 8rpx 24rpx rgba(7, 193, 96, 0.3);
	transition: all 0.2s ease;
	
	&::after {
		border: none;
	}
	
	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.4);
	}
	
	.btn-text {
		margin-left: 20rpx;
		font-size: 34rpx;
		color: #fff;
		font-weight: 500;
	}
}

/* 用户协议 */
.agreement-section {
	text-align: center;
	
	.agreement-text {
		font-size: 24rpx;
		color: #999;
		line-height: 1.6;
		
		.link-text {
			color: #07c160;
			font-weight: 500;
		}
	}
}

/* 底部信息 */
.footer-section {
	padding: 40rpx 60rpx;
	text-align: center;
	background: #fff;
	
	.copyright {
		font-size: 24rpx;
		color: #ccc;
	}
}

/* 响应式适配 */
@media screen and (max-width: 480px) {
	.header-section {
		padding: 100rpx 40rpx 60rpx;
	}
	
	.login-section {
		padding: 60rpx 40rpx 40rpx;
	}
	
	.app-name {
		font-size: 42rpx;
	}
}
</style>