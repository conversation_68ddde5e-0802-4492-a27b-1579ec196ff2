<template>
	<view class="container">
		<!-- 管理员已登录时显示管理功能 -->
		<view v-if="isAdminLoggedIn" class="admin-container">
			<!-- 头部信息栏 -->
			<view class="header">
				<view class="admin-info">
					<view class="avatar">👨‍💼</view>
					<view class="info">
						<text class="admin-name">{{ adminUser.name || '园长' }}</text>
						<text class="admin-position">系统管理员</text>
					</view>
				</view>
				<view class="header-actions">
					<button class="header-btn" @click="logout">退出</button>
				</view>
			</view>

			<!-- 主要内容区域 -->
			<view class="main-content">
				<!-- 管理功能标题 -->
				<view class="section-title">管理功能</view>

				<!-- 功能菜单区域 -->
				<view class="menu-grid">
					<view class="menu-item" @click="goToStudentAttendanceManagement">
						<view class="menu-icon">📝</view>
						<text class="menu-text">学生考勤管理</text>
					</view>
					<view class="menu-item" @click="goToTeacherAttendanceManagement">
						<view class="menu-icon">👩‍🏫</view>
						<text class="menu-text">教师考勤管理</text>
					</view>
					<view class="menu-item" @click="goToClassManagement">
						<view class="menu-icon">🏫</view>
						<text class="menu-text">班级管理</text>
					</view>
					<view class="menu-item" @click="goToUserManagement">
						<view class="menu-icon">👥</view>
						<text class="menu-text">用户管理</text>
					</view>
					<view class="menu-item" @click="goToCourseManagement">
						<view class="menu-icon">📚</view>
						<text class="menu-text">课程管理</text>
					</view>
					<view class="menu-item" @click="goToFeeManagement">
						<view class="menu-icon">💰</view>
						<text class="menu-text">园费管理</text>
					</view>
					<view class="menu-item" @click="goToStudentManagement">
						<view class="menu-icon">👶</view>
						<text class="menu-text">学生管理</text>
					</view>
					<view class="menu-item" @click="goToTeacherManagement">
						<view class="menu-icon">👨‍🏫</view>
						<text class="menu-text">教师管理</text>
					</view>
					<view class="menu-item" @click="goToInventoryManagement">
						<view class="menu-icon">📦</view>
						<text class="menu-text">库存管理</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import {toast, clearStorageSync, useRouter, getStorageSync} from '@/utils/utils.js'

export default {
	data() {
		return {
			isAdminLoggedIn: false,
			adminUser: {
				name: '',
				phone: '',
				avatar: '',
				company_name: '',
				registerDate: '',
				login_code: '',
				auth: 0
			}
		};
	},
	onLoad() {
		this.checkAdminLogin();
	},
	onShow() {
		// 每次显示页面时都检查登录状态
		this.checkAdminLogin();
	},
	methods: {
		// 检查管理员登录状态
		checkAdminLogin() {
			const adminToken = getStorageSync('admin_token');
			console.log('首页检查admin_token:', adminToken);
			this.isAdminLoggedIn = !!adminToken;

			if (adminToken) {
				console.log('有admin_token，设置登录状态');
				// 加载用户信息
				this.loadAdminUser();
			} else {
				console.log('没有admin_token，跳转到登录页面');
				// 如果没有admin_token，跳转到登录页面
				useRouter('/pages/public/login', {}, 'navigateTo');
			}
		},

		// 加载管理员用户信息
		loadAdminUser() {
			// 先从storage中获取缓存的用户信息
			const cachedUser = getStorageSync('admin_user');
			if (cachedUser) {
				this.adminUser = cachedUser;
			}
		},

		// 跳转到"我的"页面
		goToMyPage() {
			uni.switchTab({
				url: '/pages/my/index'
			});
		},

		// 学生考勤管理
		goToStudentAttendanceManagement() {
			useRouter('/pages/admin/attendance/student/index', {}, 'navigateTo');
		},

		// 教师考勤管理
		goToTeacherAttendanceManagement() {
			useRouter('/pages/admin/attendance/teacher/index', {}, 'navigateTo');
		},

		// 班级管理
		goToClassManagement() {
			useRouter('/pages/admin/class/index', {}, 'navigateTo');
		},

		// 用户管理
		goToUserManagement() {
			useRouter('/pages/admin/user/index', {}, 'navigateTo');
		},

		// 课程管理
		goToCourseManagement() {
			useRouter('/pages/admin/course/index', {}, 'navigateTo');
		},

		// 园费管理
		goToFeeManagement() {
			useRouter('/pages/admin/fee/index', {}, 'navigateTo');
		},

		// 学生管理
		goToStudentManagement() {
			useRouter('/pages/admin/student/index', {}, 'navigateTo');
		},

		// 教师管理
		goToTeacherManagement() {
			useRouter('/pages/admin/teacher/index', {}, 'navigateTo');
		},

		// 库存管理
		goToInventoryManagement() {
			useRouter('/pages/admin/inventory/index', {}, 'navigateTo');
		},

		// 退出登录
		logout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						// 清除管理员token
						clearStorageSync('admin_token');
						toast('已退出登录');
						// 跳转到我的页面
						useRouter('/pages/my/index', {}, 'switchTab');
					}
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

.admin-container {
	min-height: 100vh;
	background: #f8f9fa;
}

/* 头部区域 */
.header {
	background: #ffffff;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	padding: 40rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.admin-info {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.avatar {
	width: 80rpx;
	height: 80rpx;
	background: #e3f2fd;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 40rpx;
}

.info {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.admin-name {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.admin-position {
	font-size: 24rpx;
	color: #666;
}

.header-actions {
	.header-btn {
		background: #ff4757;
		color: white;
		border: none;
		border-radius: 40rpx;
		padding: 16rpx 32rpx;
		font-size: 24rpx;
		font-weight: 500;
	}
}

/* 主要内容区域 */
.main-content {
	padding: 40rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 30rpx;
	padding-left: 20rpx;
	border-left: 6rpx solid #4CAF50;
}

/* 功能菜单网格 */
.menu-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
}

.menu-item {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx 20rpx;
	text-align: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;
	border: 1rpx solid #f0f0f0;

	&:active {
		transform: scale(0.98);
		box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
	}
}

.menu-icon {
	font-size: 48rpx;
	margin-bottom: 16rpx;
	display: block;
}

.menu-text {
	display: block;
	font-size: 24rpx;
	color: #333333;
	font-weight: 500;
	line-height: 1.4;
}

/* 响应式适配 */
@media screen and (max-width: 480px) {
	.header {
		padding: 30rpx;
	}

	.main-content {
		padding: 30rpx;
	}

	.menu-grid {
		grid-template-columns: repeat(2, 1fr);
		gap: 24rpx;
	}

	.menu-item {
		padding: 40rpx 20rpx;
	}

	.menu-icon {
		font-size: 60rpx;
		margin-bottom: 20rpx;
	}

	.menu-text {
		font-size: 28rpx;
	}
}
</style>