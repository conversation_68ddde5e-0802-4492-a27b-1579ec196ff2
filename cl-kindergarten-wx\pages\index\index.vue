<template>
	<view class="container">
		<!-- 管理员已登录时显示管理功能 -->
		<view v-if="isAdminLoggedIn" class="admin-container">
			<!-- 主要内容区域 -->
			<view class="main-content">
				<!-- 功能菜单区域 -->
				<view class="menu-grid">
					<view class="menu-item" @click="goToUserManagement">
						<view class="menu-icon">👥</view>
						<text class="menu-text">用户管理</text>
					</view>
					<view class="menu-item" @click="goToCourseManagement">
						<view class="menu-icon">📚</view>
						<text class="menu-text">课程管理</text>
					</view>
					<view class="menu-item" @click="goToFeeManagement">
						<view class="menu-icon">💰</view>
						<text class="menu-text">园费管理</text>
					</view>
					<view class="menu-item" @click="goToStudentManagement">
						<view class="menu-icon">👶</view>
						<text class="menu-text">学生管理</text>
					</view>
					<view class="menu-item" @click="goToTeacherManagement">
						<view class="menu-icon">👨‍🏫</view>
						<text class="menu-text">教师管理</text>
					</view>
					<view class="menu-item" @click="goToInventoryManagement">
						<view class="menu-icon">📦</view>
						<text class="menu-text">库存管理</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import {toast, clearStorageSync, useRouter, getStorageSync} from '@/utils/utils.js'

export default {
	data() {
		return {
			isAdminLoggedIn: false,
			adminUser: {
				name: '',
				phone: '',
				avatar: '',
				company_name: '',
				registerDate: '',
				login_code: '',
				auth: 0
			}
		};
	},
	onLoad() {
		this.checkAdminLogin();
	},
	onShow() {
		// 每次显示页面时都检查登录状态
		this.checkAdminLogin();
	},
	methods: {
		// 检查管理员登录状态
		checkAdminLogin() {
			const adminToken = getStorageSync('admin_token');
			console.log('首页检查admin_token:', adminToken);
			this.isAdminLoggedIn = !!adminToken;

			if (adminToken) {
				console.log('有admin_token，设置登录状态');
				// 加载用户信息
				this.loadAdminUser();
			} else {
				console.log('没有admin_token，跳转到登录页面');
				// 如果没有admin_token，跳转到登录页面
				useRouter('/pages/public/login', {}, 'navigateTo');
			}
		},

		// 加载管理员用户信息
		loadAdminUser() {
			// 先从storage中获取缓存的用户信息
			const cachedUser = getStorageSync('admin_user');
			if (cachedUser) {
				this.adminUser = cachedUser;
			}
		},

		// 跳转到"我的"页面
		goToMyPage() {
			uni.switchTab({
				url: '/pages/my/index'
			});
		},
		// 用户管理
		goToUserManagement() {
			toast('用户管理功能开发中...');
			// useRouter('/pages/admin/user/index', {}, 'navigateTo');
		},

		// 课程管理
		goToCourseManagement() {
			toast('课程管理功能开发中...');
			// useRouter('/pages/admin/course/index', {}, 'navigateTo');
		},

		// 园费管理
		goToFeeManagement() {
			toast('园费管理功能开发中...');
			// useRouter('/pages/admin/fee/index', {}, 'navigateTo');
		},

		// 学生管理
		goToStudentManagement() {
			toast('学生管理功能开发中...');
			// useRouter('/pages/admin/student/index', {}, 'navigateTo');
		},

		// 教师管理
		goToTeacherManagement() {
			toast('教师管理功能开发中...');
			// useRouter('/pages/admin/teacher/index', {}, 'navigateTo');
		},

		// 库存管理
		goToInventoryManagement() {
			toast('库存管理功能开发中...');
			// useRouter('/pages/admin/inventory/index', {}, 'navigateTo');
		},

		// 退出登录
		logout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						// 清除管理员token
						clearStorageSync('admin_token');
						toast('已退出登录');
						// 跳转到我的页面
						useRouter('/pages/my/index', {}, 'switchTab');
					}
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.admin-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 头部区域 */
.header {
	background: #ffffff;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	padding: 40rpx 0 20rpx;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 40rpx;
}

.welcome-section {
	.welcome-title {
		display: block;
		font-size: 48rpx;
		font-weight: 700;
		color: #333333;
		margin-bottom: 8rpx;
	}

	.welcome-subtitle {
		display: block;
		font-size: 28rpx;
		color: #666666;
	}
}

.logout-btn {
	background: linear-gradient(135deg, #ff6b6b, #ee5a52);
	padding: 16rpx 32rpx;
	border-radius: 50rpx;
	box-shadow: 0 8rpx 20rpx rgba(255, 107, 107, 0.3);

	.logout-text {
		color: #ffffff;
		font-size: 28rpx;
		font-weight: 600;
	}
}

/* 主要内容区域 */
.main-content {
	padding: 40rpx;
}

/* 功能菜单网格 */
.menu-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 30rpx;
}

.menu-item {
	background: #ffffff;
	border-radius: 20rpx;
	padding: 40rpx 20rpx;
	text-align: center;
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
		box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);
	}
}

.menu-icon {
	font-size: 60rpx;
	margin-bottom: 20rpx;
}

.menu-text {
	display: block;
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
}

/* 响应式适配 */
@media screen and (max-width: 480px) {
	.header-content {
		padding: 0 30rpx;
	}

	.main-content {
		padding: 30rpx;
	}

	.welcome-title {
		font-size: 42rpx;
	}
}
</style>