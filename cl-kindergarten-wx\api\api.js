import request from '@/utils/request'
//使用说明
//  export const getList = data => request.get('/api/list', data, false)
//              页面调用名  请求参数       请求类型  接口地址         loading是否显示
/*页面中调用方法：（若无请求参数则留空,例：this.$api.getList()）
this.$api.getList(params).then(res => {
	
})
*/
//列表
export const getList = data => request.get('/list', data)
//登陆
export const login = data => request.post('/login', data)
//注册
export const register = data => request.post('/register', data)
//忘记密码
export const forgetPassWord = data => request.post('/forgetPassWord', data)
//登陆用户信息
export const baseInfo = data => request.get('/getInfo', data)
//保存用户信息
export const baseInfoSave = data => request.post('/saveInfo', data)
//检测版本升级
export const checkVersion = data => request.post('/checkVersion2', data,false)
//文章详情
export const detail = data => request.get('/detail', data,false)
//获取单页内容
export const page = data => request.get('/page', data)
//提交实名认证资料
export const auth = data => request.post('/auth', data)
//修改密码
export const password = data => request.post('/password', data)
//注销帐号
export const logout_account = data => request.post('/logout_account', data)
//修改手机号
export const phoneBind = data => request.get('/phoneBind', data)
//修改邮箱
export const emailBind = data => request.get('/emailBind', data)
//提交反馈
export const feedback = data => request.post('/feedback', data)
//微信授权手机号登录
export const wechatPhoneLogin = data => request.post('/wechatPhoneLogin', data)
//获取图形验证码
export const getCaptcha = () => request.get('/captchaImage')
