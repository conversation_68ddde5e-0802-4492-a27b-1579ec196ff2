<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创联幼儿园核算系统 - 整合版</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/index.css">
    <link rel="stylesheet" href="css/employee.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/app.css">
</head>
<body>
    <div class="app-container">
        <!-- 登录页面 -->
        <div class="page" id="loginPage">
            <div class="container">
                <!-- 头部logo区域 -->
                <div class="header">
                    <div class="logo">
                        <div class="logo-icon">🏫</div>
                        <h1 class="title">创联幼儿园</h1>
                        <p class="subtitle">智能核算系统</p>
                    </div>
                </div>

                <!-- 角色选择区域 -->
                <div class="role-selection">
                    <h2 class="section-title">请选择您的身份</h2>
                    <div class="role-cards">
                        <div class="role-card" data-role="employee">
                            <div class="role-icon">👩‍🏫</div>
                            <h3>员工</h3>
                            <p>考勤管理<br>学生管理<br>工资查询</p>
                        </div>
                        <div class="role-card" data-role="admin">
                            <div class="role-icon">👨‍💼</div>
                            <h3>管理员</h3>
                            <p>财务统计<br>系统管理<br>数据报表</p>
                        </div>
                    </div>
                </div>

                <!-- 登录区域 -->
                <div class="login-section" id="loginSection" style="display: none;">
                    <div class="login-form">
                        <h3 id="loginTitle">员工登录</h3>
                        <div class="form-group">
                            <input type="text" id="username" placeholder="请输入工号或手机号" class="form-input">
                        </div>
                        <div class="form-group">
                            <input type="password" id="password" placeholder="请输入密码" class="form-input">
                        </div>
                        <button class="login-btn" id="loginBtn">登录</button>
                        <div class="login-options">
                            <a href="#" class="link">忘记密码？</a>
                            <a href="#" class="link">微信快捷登录</a>
                        </div>
                    </div>
                </div>

                <!-- 底部信息 -->
                <div class="footer">
                    <p>&copy; 2024 创联幼儿园核算系统</p>
                    <p>让幼儿园财务管理更简单、更高效</p>
                </div>
            </div>
        </div>

        <!-- 员工端工作台 -->
        <div class="page" id="employeePage" style="display: none;">
            <div class="container">
                <!-- 顶部信息栏 -->
                <div class="header">
                    <div class="user-info">
                        <div class="avatar">👩‍🏫</div>
                        <div class="info">
                            <h2 id="empUserName">张老师</h2>
                            <p id="empUserPosition">小班班主任</p>
                        </div>
                    </div>
                    <div class="date-info">
                        <p id="empCurrentDate">2024年11月30日</p>
                        <p id="empAttendanceStatus" class="status-checked">✅ 已签到 08:30</p>
                    </div>
                </div>

                <!-- 今日概览 -->
                <div class="overview-section">
                    <h3 class="section-title">今日概览</h3>
                    <div class="overview-cards">
                        <div class="overview-card">
                            <div class="card-icon">👶</div>
                            <div class="card-info">
                                <h4>班级人数</h4>
                                <p class="number">25人</p>
                            </div>
                        </div>
                        <div class="overview-card">
                            <div class="card-icon">✅</div>
                            <div class="card-info">
                                <h4>已到园</h4>
                                <p class="number">23人</p>
                            </div>
                        </div>
                        <div class="overview-card">
                            <div class="card-icon">❌</div>
                            <div class="card-info">
                                <h4>未到园</h4>
                                <p class="number">2人</p>
                            </div>
                        </div>
                        <div class="overview-card">
                            <div class="card-icon">🏥</div>
                            <div class="card-info">
                                <h4>请假</h4>
                                <p class="number">1人</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="quick-actions">
                    <h3 class="section-title">快捷操作</h3>
                    <div class="action-grid">
                        <div class="action-item" onclick="showAttendancePage()">
                            <div class="action-icon">📝</div>
                            <span>学生考勤</span>
                        </div>
                        <div class="action-item" onclick="showCourseAttendance()">
                            <div class="action-icon">📚</div>
                            <span>托管考勤</span>
                        </div>
                        <div class="action-item" onclick="showStatistics()">
                            <div class="action-icon">📊</div>
                            <span>考勤统计</span>
                        </div>
                        <div class="action-item" onclick="showSalary()">
                            <div class="action-icon">💰</div>
                            <span>工资查询</span>
                        </div>
                    </div>
                </div>

                <!-- 学生考勤快捷入口 -->
                <div class="student-attendance">
                    <h3 class="section-title">学生考勤</h3>
                    <div class="student-list">
                        <div class="student-item">
                            <div class="student-avatar">👶</div>
                            <div class="student-info">
                                <h4>张小明</h4>
                                <p>已到园 08:15</p>
                            </div>
                            <div class="attendance-status present">✅</div>
                        </div>
                        <div class="student-item">
                            <div class="student-avatar">👶</div>
                            <div class="student-info">
                                <h4>李小红</h4>
                                <p>已到园 08:20</p>
                            </div>
                            <div class="attendance-status present">✅</div>
                        </div>
                        <div class="student-item">
                            <div class="student-avatar">👶</div>
                            <div class="student-info">
                                <h4>王小华</h4>
                                <p>未到园</p>
                            </div>
                            <div class="attendance-status absent">❌</div>
                        </div>
                    </div>
                    <button class="btn btn-primary" style="width: 100%; margin-top: 15px;" onclick="showAttendancePage()">查看详细考勤</button>
                </div>

                <!-- 底部导航 -->
                <div class="bottom-nav">
                    <div class="nav-item active" onclick="showEmployeePage()">
                        <div class="nav-icon">🏠</div>
                        <span>工作台</span>
                    </div>
                    <div class="nav-item" onclick="showAttendancePage()">
                        <div class="nav-icon">📝</div>
                        <span>考勤</span>
                    </div>
                    <div class="nav-item" onclick="showStudentsPage()">
                        <div class="nav-icon">👶</div>
                        <span>学生</span>
                    </div>
                    <div class="nav-item" onclick="showSalary()">
                        <div class="nav-icon">💰</div>
                        <span>工资</span>
                    </div>
                    <div class="nav-item" onclick="logout()">
                        <div class="nav-icon">👤</div>
                        <span>退出</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 员工端考勤页面 -->
        <div class="page" id="attendancePage" style="display: none;">
            <div class="container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <button class="back-btn" onclick="showEmployeePage()">←</button>
                    <h1 class="navbar-title">学生考勤</h1>
                    <button class="btn" onclick="toggleBatchMode()">批量操作</button>
                </div>

                <!-- 日期选择 -->
                <div class="date-selector">
                    <div class="date-nav">
                        <button class="date-btn" onclick="changeDate(-1)">←</button>
                        <h3 id="attendanceCurrentDate">2024年11月30日 周六</h3>
                        <button class="date-btn" onclick="changeDate(1)">→</button>
                    </div>
                    <div class="date-stats">
                        <span class="stat-item">总人数: <strong>25</strong></span>
                        <span class="stat-item">已到: <strong>23</strong></span>
                        <span class="stat-item">未到: <strong>2</strong></span>
                    </div>
                </div>

                <!-- 学生列表 -->
                <div class="student-attendance-list">
                    <div class="student-card">
                        <div class="student-avatar">👶</div>
                        <div class="student-info">
                            <h4>张小明</h4>
                            <p>学号: 2024001</p>
                            <div class="attendance-time">
                                <span class="check-in">入园: 08:15</span>
                                <span class="check-out">离园: --:--</span>
                            </div>
                        </div>
                        <div class="attendance-actions">
                            <div class="status-badge present">已到园</div>
                            <button class="action-btn" onclick="showAttendanceDetail('张小明')">详情</button>
                        </div>
                    </div>

                    <div class="student-card">
                        <div class="student-avatar">👶</div>
                        <div class="student-info">
                            <h4>李小红</h4>
                            <p>学号: 2024002</p>
                            <div class="attendance-time">
                                <span class="check-in">入园: 08:20</span>
                                <span class="check-out">离园: --:--</span>
                            </div>
                        </div>
                        <div class="attendance-actions">
                            <div class="status-badge present">已到园</div>
                            <button class="action-btn" onclick="showAttendanceDetail('李小红')">详情</button>
                        </div>
                    </div>

                    <div class="student-card">
                        <div class="student-avatar">👶</div>
                        <div class="student-info">
                            <h4>王小华</h4>
                            <p>学号: 2024003</p>
                            <div class="attendance-time">
                                <span class="check-in">入园: --:--</span>
                                <span class="check-out">离园: --:--</span>
                            </div>
                        </div>
                        <div class="attendance-actions">
                            <div class="status-badge absent">未到园</div>
                            <button class="action-btn" onclick="markAttendance('王小华')">签到</button>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作栏 -->
                <div class="quick-actions-bar">
                    <button class="quick-btn" onclick="scanQRCode()">
                        <span class="btn-icon">📱</span>
                        扫码签到
                    </button>
                    <button class="quick-btn" onclick="exportData()">
                        <span class="btn-icon">📊</span>
                        导出数据
                    </button>
                </div>
            </div>
        </div>

        <!-- 管理员端页面 -->
        <div class="page" id="adminPage" style="display: none;">
            <div class="container">
                <!-- 头部信息栏 -->
                <div class="header">
                    <div class="admin-info">
                        <div class="avatar">👨‍💼</div>
                        <div class="info">
                            <h2 id="adminName">园长</h2>
                            <p id="adminPosition">系统管理员</p>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button class="header-btn" onclick="showNotifications()">🔔</button>
                        <button class="header-btn" onclick="showSettings()">⚙️</button>
                    </div>
                </div>

                <!-- 财务概览 -->
                <div class="finance-overview">
                    <div class="overview-card income">
                        <div class="card-header">
                            <h3>总收入</h3>
                            <span class="trend up">📈 +12.5%</span>
                        </div>
                        <div class="card-amount">¥156,800</div>
                        <div class="card-detail">
                            <span>园费: ¥98,400</span>
                            <span>托管费: ¥45,600</span>
                            <span>其他: ¥12,800</span>
                        </div>
                    </div>

                    <div class="overview-card expense">
                        <div class="card-header">
                            <h3>总支出</h3>
                            <span class="trend down">📉 -5.2%</span>
                        </div>
                        <div class="card-amount">¥89,200</div>
                        <div class="card-detail">
                            <span>工资: ¥52,000</span>
                            <span>餐费: ¥18,500</span>
                            <span>运营: ¥18,700</span>
                        </div>
                    </div>

                    <div class="overview-card profit">
                        <div class="card-header">
                            <h3>净利润</h3>
                            <span class="trend up">📊 43.1%</span>
                        </div>
                        <div class="card-amount">¥67,600</div>
                        <div class="card-detail">
                            <span>利润率: 43.1%</span>
                            <span>环比: +18.3%</span>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="quick-actions">
                    <h3 class="section-title">快捷操作</h3>
                    <div class="action-grid">
                        <div class="action-item" onclick="showFinancePage()">
                            <div class="action-icon">💰</div>
                            <span>财务管理</span>
                        </div>
                        <div class="action-item" onclick="showBillsPage()">
                            <div class="action-icon">📋</div>
                            <span>账单管理</span>
                        </div>
                        <div class="action-item" onclick="showReportsPage()">
                            <div class="action-icon">📊</div>
                            <span>数据报表</span>
                        </div>
                        <div class="action-item" onclick="showSettingsPage()">
                            <div class="action-icon">⚙️</div>
                            <span>系统设置</span>
                        </div>
                    </div>
                </div>

                <!-- 收费统计 -->
                <div class="payment-stats">
                    <h3 class="section-title">班级缴费统计</h3>
                    <div class="stats-list">
                        <div class="stats-item">
                            <div class="class-info">
                                <h4>小班</h4>
                                <p>40人</p>
                            </div>
                            <div class="payment-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 95%"></div>
                                </div>
                                <span class="progress-text">95% (38/40)</span>
                            </div>
                            <div class="payment-amount">¥59,280</div>
                        </div>

                        <div class="stats-item">
                            <div class="class-info">
                                <h4>中班</h4>
                                <p>38人</p>
                            </div>
                            <div class="payment-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 92%"></div>
                                </div>
                                <span class="progress-text">92% (35/38)</span>
                            </div>
                            <div class="payment-amount">¥54,600</div>
                        </div>

                        <div class="stats-item">
                            <div class="class-info">
                                <h4>大班</h4>
                                <p>36人</p>
                            </div>
                            <div class="payment-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 88%"></div>
                                </div>
                                <span class="progress-text">88% (32/36)</span>
                            </div>
                            <div class="payment-amount">¥49,920</div>
                        </div>
                    </div>
                </div>

                <!-- 最新动态 -->
                <div class="recent-activities">
                    <h3 class="section-title">最新动态</h3>
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon">💰</div>
                            <div class="activity-content">
                                <h4>张小明家长缴费</h4>
                                <p>园费 ¥1,560 - 刚刚</p>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">📋</div>
                            <div class="activity-content">
                                <h4>11月账单已生成</h4>
                                <p>共114份账单 - 5分钟前</p>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">👩‍🏫</div>
                            <div class="activity-content">
                                <h4>李老师提交考勤</h4>
                                <p>小班考勤已确认 - 10分钟前</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部导航 -->
                <div class="bottom-nav">
                    <div class="nav-item active" onclick="showAdminPage()">
                        <div class="nav-icon">🏠</div>
                        <span>首页</span>
                    </div>
                    <div class="nav-item" onclick="showFinancePage()">
                        <div class="nav-icon">💰</div>
                        <span>财务</span>
                    </div>
                    <div class="nav-item" onclick="showReportsPage()">
                        <div class="nav-icon">📊</div>
                        <span>报表</span>
                    </div>
                    <div class="nav-item" onclick="showSettingsPage()">
                        <div class="nav-icon">⚙️</div>
                        <span>设置</span>
                    </div>
                    <div class="nav-item" onclick="logout()">
                        <div class="nav-icon">👤</div>
                        <span>退出</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
