<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创联幼儿园核算系统 - 整合版</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/index.css">
    <link rel="stylesheet" href="css/employee.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/app.css">
</head>
<body>
    <div class="app-container">
        <!-- 登录页面 -->
        <div class="page" id="loginPage">
            <div class="container">
                <!-- 头部logo区域 -->
                <div class="header">
                    <div class="logo">
                        <div class="logo-icon">🏫</div>
                        <h1 class="title">创联幼儿园</h1>
                        <p class="subtitle">智能核算系统</p>
                    </div>
                </div>

                <!-- 角色选择区域 -->
                <div class="role-selection">
                    <h2 class="section-title">请选择您的身份</h2>
                    <div class="role-cards">
                        <div class="role-card" data-role="employee">
                            <div class="role-icon">👩‍🏫</div>
                            <h3>员工</h3>
                            <p>考勤管理<br>学生管理<br>工资查询</p>
                        </div>
                        <div class="role-card" data-role="admin">
                            <div class="role-icon">👨‍💼</div>
                            <h3>管理员</h3>
                            <p>财务统计<br>系统管理<br>数据报表</p>
                        </div>
                    </div>
                </div>

                <!-- 登录区域 -->
                <div class="login-section" id="loginSection" style="display: none;">
                    <div class="login-form">
                        <h3 id="loginTitle">员工登录</h3>
                        <div class="form-group">
                            <input type="text" id="username" placeholder="请输入工号或手机号" class="form-input">
                        </div>
                        <div class="form-group">
                            <input type="password" id="password" placeholder="请输入密码" class="form-input">
                        </div>
                        <button class="login-btn" id="loginBtn">登录</button>
                        <div class="login-options">
                            <a href="#" class="link">忘记密码？</a>
                            <a href="#" class="link">微信快捷登录</a>
                        </div>
                    </div>
                </div>

                <!-- 底部信息 -->
                <div class="footer">
                    <p>&copy; 2024 创联幼儿园核算系统</p>
                    <p>让幼儿园财务管理更简单、更高效</p>
                </div>
            </div>
        </div>

        <!-- 员工端工作台 -->
        <div class="page" id="employeePage" style="display: none;">
            <div class="container">
                <!-- 顶部信息栏 -->
                <div class="header">
                    <div class="user-info">
                        <div class="avatar">👩‍🏫</div>
                        <div class="info">
                            <h2 id="empUserName">张老师</h2>
                            <p id="empUserPosition">小班班主任</p>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button class="header-btn" onclick="logout()">退出</button>
                    </div>
                </div>

                <!-- 管理功能 -->
                <div class="management-section">
                    <h3 class="section-title">管理功能</h3>
                    <div class="management-grid">
                        <div class="management-item" onclick="showUserManagement()">
                            <div class="management-icon">👥</div>
                            <span>用户管理</span>
                        </div>
                        <div class="management-item" onclick="showCourseManagement()">
                            <div class="management-icon">📚</div>
                            <span>课程管理</span>
                        </div>
                        <div class="management-item" onclick="showTuitionManagement()">
                            <div class="management-icon">💰</div>
                            <span>园费管理</span>
                        </div>
                        <div class="management-item" onclick="showStudentManagement()">
                            <div class="management-icon">👶</div>
                            <span>学生管理</span>
                        </div>
                        <div class="management-item" onclick="showTeacherManagement()">
                            <div class="management-icon">👩‍🏫</div>
                            <span>教师管理</span>
                        </div>
                        <div class="management-item" onclick="showInventoryManagement()">
                            <div class="management-icon">📦</div>
                            <span>库存管理</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- 管理员端页面 -->
        <div class="page" id="adminPage" style="display: none;">
            <div class="container">
                <!-- 头部信息栏 -->
                <div class="header">
                    <div class="admin-info">
                        <div class="avatar">👨‍💼</div>
                        <div class="info">
                            <h2 id="adminName">园长</h2>
                            <p id="adminPosition">系统管理员</p>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button class="header-btn" onclick="logout()">退出</button>
                    </div>
                </div>

                <!-- 管理功能 -->
                <div class="management-section">
                    <h3 class="section-title">管理功能</h3>
                    <div class="management-grid">
                        <div class="management-item" onclick="showUserManagement()">
                            <div class="management-icon">👥</div>
                            <span>用户管理</span>
                        </div>
                        <div class="management-item" onclick="showCourseManagement()">
                            <div class="management-icon">📚</div>
                            <span>课程管理</span>
                        </div>
                        <div class="management-item" onclick="showTuitionManagement()">
                            <div class="management-icon">💰</div>
                            <span>园费管理</span>
                        </div>
                        <div class="management-item" onclick="showStudentManagement()">
                            <div class="management-icon">👶</div>
                            <span>学生管理</span>
                        </div>
                        <div class="management-item" onclick="showTeacherManagement()">
                            <div class="management-icon">👩‍🏫</div>
                            <span>教师管理</span>
                        </div>
                        <div class="management-item" onclick="showInventoryManagement()">
                            <div class="management-icon">📦</div>
                            <span>库存管理</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学生管理页面示例 -->
        <div class="page" id="studentManagementPage" style="display: none;">
            <div class="container">
                <!-- 导航栏 -->
                <div class="navbar">
                    <button class="back-btn" onclick="goBack()">←</button>
                    <h1 class="navbar-title">学生管理</h1>
                    <button class="btn" onclick="addStudent()">添加</button>
                </div>

                <!-- 搜索栏 -->
                <div class="search-section">
                    <input type="text" class="search-input" placeholder="搜索学生姓名或学号" id="studentSearch">
                    <button class="search-btn" onclick="searchStudents()">🔍</button>
                </div>

                <!-- 学生列表 -->
                <div class="student-list-section">
                    <div class="student-item-card">
                        <div class="student-avatar">👶</div>
                        <div class="student-details">
                            <h4>张小明</h4>
                            <p>学号: 2024001 | 小班</p>
                            <p>家长: 张先生 | 电话: 138****1234</p>
                        </div>
                        <div class="student-actions">
                            <button class="action-btn edit" onclick="editStudent(1)">编辑</button>
                            <button class="action-btn delete" onclick="deleteStudent(1)">删除</button>
                        </div>
                    </div>

                    <div class="student-item-card">
                        <div class="student-avatar">👶</div>
                        <div class="student-details">
                            <h4>李小红</h4>
                            <p>学号: 2024002 | 小班</p>
                            <p>家长: 李女士 | 电话: 139****5678</p>
                        </div>
                        <div class="student-actions">
                            <button class="action-btn edit" onclick="editStudent(2)">编辑</button>
                            <button class="action-btn delete" onclick="deleteStudent(2)">删除</button>
                        </div>
                    </div>

                    <div class="student-item-card">
                        <div class="student-avatar">👶</div>
                        <div class="student-details">
                            <h4>王小华</h4>
                            <p>学号: 2024003 | 中班</p>
                            <p>家长: 王先生 | 电话: 137****9012</p>
                        </div>
                        <div class="student-actions">
                            <button class="action-btn edit" onclick="editStudent(3)">编辑</button>
                            <button class="action-btn delete" onclick="deleteStudent(3)">删除</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
