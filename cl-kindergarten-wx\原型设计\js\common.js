// 通用JavaScript功能

// 全局配置
const CONFIG = {
    API_BASE_URL: '/api',
    TOKEN_KEY: 'kg_token',
    USER_KEY: 'kg_user',
    ROLE_KEY: 'kg_role'
};

// 工具函数
const Utils = {
    // 获取本地存储
    getStorage(key) {
        try {
            return JSON.parse(localStorage.getItem(key));
        } catch (e) {
            return localStorage.getItem(key);
        }
    },

    // 设置本地存储
    setStorage(key, value) {
        if (typeof value === 'object') {
            localStorage.setItem(key, JSON.stringify(value));
        } else {
            localStorage.setItem(key, value);
        }
    },

    // 删除本地存储
    removeStorage(key) {
        localStorage.removeItem(key);
    },

    // 清空本地存储
    clearStorage() {
        localStorage.clear();
    },

    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hour = String(d.getHours()).padStart(2, '0');
        const minute = String(d.getMinutes()).padStart(2, '0');
        const second = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hour)
            .replace('mm', minute)
            .replace('ss', second);
    },

    // 格式化金额
    formatMoney(amount) {
        return '¥' + Number(amount).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
    },

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// HTTP请求封装
const Http = {
    // GET请求
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = `${CONFIG.API_BASE_URL}${url}${queryString ? '?' + queryString : ''}`;
        
        try {
            const response = await fetch(fullUrl, {
                method: 'GET',
                headers: this.getHeaders()
            });
            return await this.handleResponse(response);
        } catch (error) {
            throw this.handleError(error);
        }
    },

    // POST请求
    async post(url, data = {}) {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}${url}`, {
                method: 'POST',
                headers: this.getHeaders(),
                body: JSON.stringify(data)
            });
            return await this.handleResponse(response);
        } catch (error) {
            throw this.handleError(error);
        }
    },

    // PUT请求
    async put(url, data = {}) {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}${url}`, {
                method: 'PUT',
                headers: this.getHeaders(),
                body: JSON.stringify(data)
            });
            return await this.handleResponse(response);
        } catch (error) {
            throw this.handleError(error);
        }
    },

    // DELETE请求
    async delete(url) {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}${url}`, {
                method: 'DELETE',
                headers: this.getHeaders()
            });
            return await this.handleResponse(response);
        } catch (error) {
            throw this.handleError(error);
        }
    },

    // 获取请求头
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        const token = Utils.getStorage(CONFIG.TOKEN_KEY);
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        
        return headers;
    },

    // 处理响应
    async handleResponse(response) {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.code !== 200) {
            throw new Error(data.message || '请求失败');
        }
        
        return data;
    },

    // 处理错误
    handleError(error) {
        console.error('HTTP Error:', error);
        return {
            success: false,
            message: error.message || '网络请求失败'
        };
    }
};

// 消息提示
const Toast = {
    show(message, type = 'info', duration = 3000) {
        // 移除已存在的toast
        const existingToast = document.querySelector('.toast');
        if (existingToast) {
            existingToast.remove();
        }

        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        // 添加样式
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            padding: '12px 20px',
            borderRadius: '8px',
            color: 'white',
            fontSize: '14px',
            zIndex: '9999',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        // 设置背景色
        const colors = {
            success: '#4CAF50',
            error: '#f44336',
            warning: '#ff9800',
            info: '#2196F3'
        };
        toast.style.backgroundColor = colors[type] || colors.info;

        // 添加到页面
        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    },

    success(message, duration) {
        this.show(message, 'success', duration);
    },

    error(message, duration) {
        this.show(message, 'error', duration);
    },

    warning(message, duration) {
        this.show(message, 'warning', duration);
    },

    info(message, duration) {
        this.show(message, 'info', duration);
    }
};

// 用户认证
const Auth = {
    // 登录
    async login(username, password, role) {
        try {
            const response = await Http.post('/auth/login', {
                username,
                password,
                role
            });

            if (response.success) {
                Utils.setStorage(CONFIG.TOKEN_KEY, response.data.token);
                Utils.setStorage(CONFIG.USER_KEY, response.data.user);
                Utils.setStorage(CONFIG.ROLE_KEY, role);
                return response;
            } else {
                throw new Error(response.message);
            }
        } catch (error) {
            throw error;
        }
    },

    // 登出
    logout() {
        Utils.removeStorage(CONFIG.TOKEN_KEY);
        Utils.removeStorage(CONFIG.USER_KEY);
        Utils.removeStorage(CONFIG.ROLE_KEY);
        window.location.href = '/index.html';
    },

    // 检查登录状态
    isLoggedIn() {
        return !!Utils.getStorage(CONFIG.TOKEN_KEY);
    },

    // 获取当前用户
    getCurrentUser() {
        return Utils.getStorage(CONFIG.USER_KEY);
    },

    // 获取当前角色
    getCurrentRole() {
        return Utils.getStorage(CONFIG.ROLE_KEY);
    }
};

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    if (Auth.isLoggedIn() && window.location.pathname === '/index.html') {
        const role = Auth.getCurrentRole();
        if (role === 'employee') {
            window.location.href = '/pages/employee/dashboard.html';
        } else if (role === 'admin') {
            window.location.href = '/pages/admin/dashboard.html';
        }
    }
});
