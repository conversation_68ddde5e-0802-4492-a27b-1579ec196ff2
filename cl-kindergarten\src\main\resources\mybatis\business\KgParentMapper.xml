<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgParentMapper">
    
    <resultMap type="KgParent" id="KgParentResult">
        <result property="parentId"          column="parent_id"          />
        <result property="parentName"        column="parent_name"        />
        <result property="parentPhone"       column="parent_phone"       />
        <result property="wechatOpenid"      column="wechat_openid"      />
        <result property="idCard"            column="id_card"            />
        <result property="relationshipType"  column="relationship_type"  />
        <result property="emergencyContact"  column="emergency_contact"  />
        <result property="emergencyPhone"    column="emergency_phone"    />
        <result property="address"           column="address"            />
        <result property="workUnit"          column="work_unit"          />
        <result property="occupation"        column="occupation"         />
        <result property="status"            column="status"             />
        <result property="bindStatus"        column="bind_status"        />
        <result property="registerDate"      column="register_date"      />
        <result property="comId"             column="com_id"             />
        <result property="studentCount"      column="student_count"      />
        <result property="createBy"          column="create_by"          />
        <result property="createTime"        column="create_time"        />
        <result property="updateBy"          column="update_by"          />
        <result property="updateTime"        column="update_time"        />
        <result property="remark"            column="remark"             />
    </resultMap>

    <sql id="selectKgParentVo">
        SELECT 
            MIN(student_id) as parent_id,
            parent_name,
            parent_phone,
            MAX(wechat_openid) as wechat_openid,
            '' as id_card,
            '父母' as relationship_type,
            MAX(emergency_contact) as emergency_contact,
            MAX(emergency_phone) as emergency_phone,
            MAX(address) as address,
            '' as work_unit,
            '' as occupation,
            MAX(status) as status,
            CASE WHEN MAX(wechat_openid) IS NOT NULL AND MAX(wechat_openid) != '' THEN '1' ELSE '0' END as bind_status,
            MAX(enrollment_date) as register_date,
            MAX(com_id) as com_id,
            COUNT(*) as student_count,
            MAX(create_by) as create_by,
            MAX(create_time) as create_time,
            MAX(update_by) as update_by,
            MAX(update_time) as update_time,
            MAX(remark) as remark
        from kg_student
        where parent_phone is not null and parent_phone != ''
    </sql>

    <select id="selectKgParentList" parameterType="KgParent" resultMap="KgParentResult">
        <include refid="selectKgParentVo"/>
        <where>  
            <if test="parentName != null  and parentName != ''"> and parent_name like concat('%', #{parentName}, '%')</if>
            <if test="parentPhone != null  and parentPhone != ''"> and parent_phone = #{parentPhone}</if>
            <if test="wechatOpenid != null  and wechatOpenid != ''"> and wechat_openid = #{wechatOpenid}</if>
            <if test="relationshipType != null  and relationshipType != ''"> and relationship_type = #{relationshipType}</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="bindStatus != null  and bindStatus != ''"> 
                <if test="bindStatus == '1'">
                    and wechat_openid is not null and wechat_openid != ''
                </if>
                <if test="bindStatus == '0'">
                    and (wechat_openid is null or wechat_openid = '')
                </if>
            </if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
        GROUP BY parent_phone, parent_name
        ORDER BY create_time DESC
    </select>
    
    <select id="selectKgParentByParentId" parameterType="Long" resultMap="KgParentResult">
        <include refid="selectKgParentVo"/>
        AND student_id = #{parentId}
        GROUP BY parent_phone, parent_name
        limit 1
    </select>

    <select id="selectKgParentByPhone" parameterType="String" resultMap="KgParentResult">
        <include refid="selectKgParentVo"/>
        AND parent_phone = #{parentPhone}
        GROUP BY parent_phone, parent_name
        limit 1
    </select>

    <select id="selectKgParentByWechatOpenid" parameterType="String" resultMap="KgParentResult">
        <include refid="selectKgParentVo"/>
        AND MAX(wechat_openid) = #{wechatOpenid}
        GROUP BY parent_phone, parent_name
        HAVING MAX(wechat_openid) = #{wechatOpenid}
        limit 1
    </select>

    <select id="countStudentsByParentId" parameterType="Long" resultType="int">
        select count(*) from kg_student 
        where parent_phone = (select parent_phone from kg_student where student_id = #{parentId})
    </select>
        
    <insert id="insertKgParent" parameterType="KgParent">
        <!-- 由于家长信息存储在kg_student表中，这里实际是更新学生表的家长信息 -->
        <!-- 实际应用中需要配合学生信息一起维护 -->
        <selectKey keyProperty="parentId" resultType="long" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into kg_student(
            student_code,
            student_name,
            parent_name,
            parent_phone,
            emergency_contact,
            emergency_phone,
            address,
            wechat_openid,
            status,
            enrollment_date,
            com_id,
            create_by,
            create_time,
            remark
        ) values (
            CONCAT('TEMP_', #{parentPhone}),
            '临时学生',
            #{parentName},
            #{parentPhone},
            #{emergencyContact},
            #{emergencyPhone},
            #{address},
            #{wechatOpenid},
            #{status},
            #{registerDate},
            #{comId},
            #{createBy},
            #{createTime},
            #{remark}
        )
    </insert>

    <update id="updateKgParent" parameterType="KgParent">
        update kg_student
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentName != null and parentName != ''">parent_name = #{parentName},</if>
            <if test="parentPhone != null and parentPhone != ''">parent_phone = #{parentPhone},</if>
            <if test="wechatOpenid != null">wechat_openid = #{wechatOpenid},</if>
            <if test="emergencyContact != null">emergency_contact = #{emergencyContact},</if>
            <if test="emergencyPhone != null">emergency_phone = #{emergencyPhone},</if>
            <if test="address != null">address = #{address},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where parent_phone = (select temp.parent_phone from (select parent_phone from kg_student where student_id = #{parentId}) temp)
    </update>

    <delete id="deleteKgParentByParentId" parameterType="Long">
        <!-- 删除家长实际上是删除相关学生记录，谨慎操作 -->
        delete from kg_student where student_id = #{parentId}
    </delete>

    <delete id="deleteKgParentByParentIds" parameterType="String">
        delete from kg_student where student_id in 
        <foreach item="parentId" collection="array" open="(" separator="," close=")">
            #{parentId}
        </foreach>
    </delete>

    <update id="updateParentBindStatus">
        update kg_student set 
            wechat_openid = case when #{bindStatus} = '0' then null else wechat_openid end,
            update_time = sysdate()
        where student_id in
        <foreach item="parentId" collection="parentIds" open="(" separator="," close=")">
            #{parentId}
        </foreach>
    </update>

</mapper>
